import { supabase } from '@/utils/supabase';
import { PostgrestError } from '@supabase/supabase-js';

/**
 * 删除分支
 *
 * @param segmentId 段落ID
 * @returns 操作结果
 */
export async function deleteBranch(
  segmentId: string
): Promise<{ success: boolean; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return {
      success: false,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };
  }

  try {
    // 首先检查段落是否存在并且用户是否有权限删除
    const { data: segment, error: checkError } = await supabase
      .from('story_segments')
      .select('id, author_id, story_id, parent_segment_id')
      .eq('id', segmentId)
      .single();

    if (checkError || !segment) {
      throw checkError || new Error('Segment not found');
    }

    // 检查用户是否是段落的作者
    if (segment.author_id !== user.id) {
      return {
        success: false,
        error: {
          message: 'Permission denied: You can only delete your own branches',
          details: '',
          hint: '',
          code: '403',
        } as PostgrestError,
      };
    }

    // 检查是否有子分支
    const { data: children, error: childrenError } = await supabase
      .from('story_segments')
      .select('id')
      .eq('parent_segment_id', segmentId);

    if (childrenError) {
      throw childrenError;
    }

    // 如果有子分支，不允许删除
    if (children && children.length > 0) {
      return {
        success: false,
        error: {
          message:
            'Cannot delete a branch with children. Delete all child branches first.',
          details: '',
          hint: '',
          code: '400',
        } as PostgrestError,
      };
    }

    // 删除段落
    const { error } = await supabase
      .from('story_segments')
      .delete()
      .eq('id', segmentId);

    if (error) {
      throw error;
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Error deleting branch:', error);
    return {
      success: false,
      error: error as PostgrestError,
    };
  }
}
