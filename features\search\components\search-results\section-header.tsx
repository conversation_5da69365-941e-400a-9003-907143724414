import React from 'react';
import { useTranslation } from 'react-i18next';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { HStack } from '@/components/ui/hstack';

interface SectionHeaderProps {
  title: string;
  showSeeMore?: boolean;
  onSeeMorePress?: () => void;
}

export function SectionHeader({
  title,
  showSeeMore = false,
  onSeeMorePress,
}: SectionHeaderProps) {
  const { t } = useTranslation();

  return (
    <HStack className="justify-between items-center px-4 pt-6 pb-3">
      <Text className="text-lg font-bold text-typography-900 dark:text-typography-50">
        {title}
      </Text>
      {showSeeMore && onSeeMorePress && (
        <Pressable onPress={onSeeMorePress}>
          <Text className="text-sm text-primary-500">
            {t('social.search.seeMore', '查看更多')}
          </Text>
        </Pressable>
      )}
    </HStack>
  );
}
