import React from 'react';
import { useTranslation } from 'react-i18next';
import { UserPlus, Check, MessageCircle } from 'lucide-react-native';
import {
  Button,
  ButtonText,
  ButtonIcon,
  ButtonSpinner,
} from '@/components/ui/m3e-button';
import { Box } from '@/components/ui/box';

interface UserActionButtonsProps {
  isFollowing: boolean;
  isFollowingInProgress: boolean;
  onFollowToggle: () => void;
  onSendMessage: () => void;
}

export default function UserActionButtons({
  isFollowing,
  isFollowingInProgress,
  onFollowToggle,
  onSendMessage,
}: UserActionButtonsProps) {
  const { t } = useTranslation();

  return (
    <Box className="flex-row p-4 justify-center gap-3">
      <Button
        action={isFollowing ? 'secondary' : 'primary'}
        variant={isFollowing ? 'outline' : 'solid'}
        size="sm"
        onPress={onFollowToggle}
        isDisabled={isFollowingInProgress}
        className="flex-1 flex-row items-center justify-center rounded-full py-2"
      >
        {isFollowingInProgress ? (
          <ButtonSpinner />
        ) : (
          <>
            <ButtonIcon
              as={isFollowing ? Check : UserPlus}
              size={16}
              className="mr-2"
            />
            <ButtonText>
              {isFollowing
                ? t('social.userProfile.followingStatus', '已关注')
                : t('social.userProfile.follow', '关注')}
            </ButtonText>
          </>
        )}
      </Button>

      <Button
        action="secondary"
        variant="outline"
        size="sm"
        onPress={onSendMessage}
        className="flex-1 flex-row items-center justify-center rounded-full py-2"
      >
        <ButtonIcon as={MessageCircle} size={16} className="mr-2" />
        <ButtonText>{t('social.userProfile.message', '发消息')}</ButtonText>
      </Button>
    </Box>
  );
}
