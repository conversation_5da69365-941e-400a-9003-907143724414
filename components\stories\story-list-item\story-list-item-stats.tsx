import React from 'react';
import { Eye, Heart, Calendar } from 'lucide-react-native';

// Import gluestack-ui components
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { HStack } from '@/components/ui/hstack';

interface StoryListItemStatsProps {
  views: number;
  likes: number;
  updatedAt: string | undefined;
  formatDate: (dateString: string | undefined) => string;
}

export function StoryListItemStats({
  views,
  likes,
  updatedAt,
  formatDate,
}: StoryListItemStatsProps) {
  return (
    <HStack className="justify-between items-center my-1">
      <HStack className="items-center space-x-1">
        <Calendar
          size={12}
          className="text-secondary-500 dark:text-secondary-400"
        />
        <Text className="text-xs text-secondary-500 dark:text-secondary-400">
          {formatDate(updatedAt)}
        </Text>
      </HStack>

      <HStack className="space-x-3">
        <HStack className="items-center space-x-1">
          <Eye
            size={12}
            className="text-secondary-500 dark:text-secondary-400"
          />
          <Text className="text-xs text-secondary-500 dark:text-secondary-400">
            {views}
          </Text>
        </HStack>

        <HStack className="items-center space-x-1">
          <Heart
            size={12}
            className="text-secondary-500 dark:text-secondary-400"
          />
          <Text className="text-xs text-secondary-500 dark:text-secondary-400">
            {likes}
          </Text>
        </HStack>
      </HStack>
    </HStack>
  );
}
