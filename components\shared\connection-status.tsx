import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNetworkStatus } from '@/hooks/use-network-status';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
// import { Button } from '@/components/ui/button';
// import { ButtonText } from '@/components/ui/m3e-button';
import { Spinner } from '@/components/ui/spinner';

interface ConnectionStatusProps {
  showWhenHealthy?: boolean;
}

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  showWhenHealthy = false,
}) => {
  const { t } = useTranslation();
  const {
    isConnected,
    isSupabaseHealthy,
    connectionType,
    isChecking,
    lastChecked,
    checkHealth,
  } = useNetworkStatus();

  // 如果连接正常且不需要显示，则不渲染
  if (isConnected && isSupabaseHealthy && !showWhenHealthy) {
    return null;
  }

  const getStatusColor = () => {
    if (!isConnected) return 'bg-error-500';
    if (!isSupabaseHealthy) return 'bg-warning-500';
    return 'bg-success-500';
  };

  const getStatusText = () => {
    if (!isConnected) {
      return t('network.disconnected', '网络连接已断开');
    }
    if (!isSupabaseHealthy) {
      return t('network.serviceUnavailable', '服务暂时不可用');
    }
    return t('network.connected', '连接正常');
  };

  const getStatusIcon = () => {
    if (isChecking) {
      return <Spinner className="text-white" size="small" />;
    }
    return null;
  };

  return (
    <Box className={`${getStatusColor()} p-3 m-2 rounded-lg`}>
      <Box className="flex-row items-center justify-between">
        <Box className="flex-row items-center flex-1">
          {getStatusIcon()}
          <Box className="ml-2 flex-1">
            <Text className="text-white font-medium">{getStatusText()}</Text>
            {connectionType && (
              <Text className="text-white text-sm opacity-80">
                {t('network.connectionType', '连接类型')}: {connectionType}
              </Text>
            )}
            {lastChecked && (
              <Text className="text-white text-xs opacity-70">
                {t('network.lastChecked', '上次检查')}:{' '}
                {lastChecked.toLocaleTimeString()}
              </Text>
            )}
          </Box>
        </Box>

        {/* Button 组件暂时注释，等待 m3e-button 模块导出问题解决 */}
        {/*
        {!isSupabaseHealthy && isConnected && (
          <Button
            variant="outline"
            size="sm"
            onPress={checkHealth}
            disabled={isChecking}
            className="border-white"
          >
            <ButtonText className="text-white">
              {t('network.retry', '重试')}
            </ButtonText>
          </Button>
        )}
        */}
      </Box>
    </Box>
  );
};
