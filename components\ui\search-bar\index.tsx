import React from 'react';
import { HStack } from '@/components/ui/hstack';
import { Input } from '@/components/ui/input';
import { InputField } from '@/components/ui/input';
import { InputSlot } from '@/components/ui/input';
import { InputIcon } from '@/components/ui/input';
import { Button } from '@/components/ui/m3e-button';
import { ButtonIcon } from '@/components/ui/m3e-button';
import { Search, X } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';
import { useColorScheme } from 'nativewind';

interface SearchBarProps {
  value?: string;
  onChangeText?: (text: string) => void;
  onSearch?: (text: string) => void;
  autoFocus?: boolean;
  placeholder?: string;
}

export default function SearchBar({
  value,
  onChangeText,
  onSearch,
  autoFocus = false,
  placeholder,
}: SearchBarProps) {
  const { t } = useTranslation();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [searchText, setSearchText] = React.useState(value || '');

  // 如果外部 value 变化，更新内部状态
  React.useEffect(() => {
    if (value !== undefined) {
      setSearchText(value);
    }
  }, [value]);

  const handleChangeText = (text: string) => {
    // 如果提供了外部 onChangeText，则调用它
    if (onChangeText) {
      onChangeText(text);
    } else {
      // 否则使用内部状态
      setSearchText(text);
    }
  };

  const handleSubmit = () => {
    const textToSearch = value !== undefined ? value : searchText;
    if (onSearch && textToSearch.trim()) {
      onSearch(textToSearch);
    }
  };

  const handleClear = () => {
    if (onChangeText) {
      onChangeText('');
    }
    setSearchText('');
  };

  return (
    <Input
      className={`h-11 rounded-lg border ${
        isDark
          ? 'border-outline-700 bg-background-900'
          : 'border-outline-200 bg-background-50'
      }`}
    >
      <InputSlot className="pl-3">
        <InputIcon
          as={Search}
          className={isDark ? 'text-typography-500' : 'text-typography-400'}
          size="sm"
        />
      </InputSlot>

      <InputField
        className={`h-full text-base font-normal ${
          isDark ? 'text-typography-50' : 'text-typography-900'
        }`}
        placeholder={
          placeholder || t('searchPlaceholder', '搜索故事、作者、标签...')
        }
        value={value !== undefined ? value : searchText}
        onChangeText={handleChangeText}
        onSubmitEditing={handleSubmit}
        returnKeyType="search"
        autoFocus={autoFocus}
      />

      {(value || searchText) && (value || searchText).length > 0 && (
        <InputSlot className="pr-3">
          <Button
            action="secondary"
            variant="link"
            size="xs"
            onPress={handleClear}
            className="p-1"
          >
            <ButtonIcon as={X} size="sm" />
          </Button>
        </InputSlot>
      )}
    </Input>
  );
}
