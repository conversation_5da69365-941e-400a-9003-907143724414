import React, { useState } from 'react';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Comment, addStoryComment } from '@/api/comments';
import { useAuthStore } from '@/lib/store/auth-store';
import { Ionicons } from '@expo/vector-icons';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Button, ButtonText, ButtonSpinner } from '@/components/ui/m3e-button';
import { Textarea, TextareaInput } from '@/components/ui/textarea';

interface CommentInputProps {
  storyId: string;
  parentCommentId?: string | null;
  onCommentAdded: (comment: Comment) => void;
  onCancel?: () => void;
  placeholder?: string;
}

export function CommentInput({
  storyId,
  parentCommentId = null,
  onCommentAdded,
  onCancel,
  placeholder,
}: CommentInputProps) {
  const { t } = useTranslation();
  const currentUser = useAuthStore((state) => state.user);

  const [content, setContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (!currentUser) {
      Alert.alert(
        t('error', '错误'),
        t('comments.loginRequired', '请先登录后再评论')
      );
      return;
    }

    if (content.trim() === '') {
      Alert.alert(
        t('error', '错误'),
        t('comments.emptyCommentError', '评论内容不能为空')
      );
      return;
    }

    setIsSubmitting(true);

    try {
      const { data, error } = await addStoryComment(
        storyId,
        content,
        parentCommentId || undefined
      );

      if (error) throw error;

      if (data) {
        onCommentAdded(data);
        setContent('');
      }
    } catch (err) {
      console.error('Failed to add comment:', err);
      Alert.alert(
        t('error', '错误'),
        t('comments.addError', '发表评论失败，请重试')
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box className="bg-background-100 dark:bg-background-800 rounded-lg p-3 mb-4">
      <Textarea size="md" h={80}>
        <TextareaInput
          value={content}
          onChangeText={setContent}
          placeholder={placeholder || t('comments.placeholder', '发表评论...')}
          className="p-2 text-typography-900 dark:text-typography-100"
          multiline
          maxLength={1000}
        />
      </Textarea>

      <Box className="flex-row justify-end items-center mt-2">
        {onCancel && (
          <Button
            variant="link"
            onPress={onCancel}
            isDisabled={isSubmitting}
            className="mr-2"
          >
            <ButtonText className="text-typography-500 dark:text-typography-400 text-sm font-medium">
              {t('cancel', '取消')}
            </ButtonText>
          </Button>
        )}

        <Button
          variant="solid"
          action="primary"
          onPress={handleSubmit}
          isDisabled={content.trim() === '' || isSubmitting}
          className={`flex-row items-center py-1 px-3 rounded ${
            content.trim() === '' || isSubmitting ? 'opacity-60' : ''
          }`}
        >
          {isSubmitting ? (
            <ButtonSpinner color="$background" />
          ) : (
            <>
              <Ionicons name="send" size={16} color="white" />
              <ButtonText className="text-background-50 dark:text-background-950 text-sm ml-1">
                {t('comments.submit', '发送')}
              </ButtonText>
            </>
          )}
        </Button>
      </Box>
    </Box>
  );
}
