import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Send, Trash2, MessageCircle } from 'lucide-react-native';
import { BranchComment } from '@/api/stories';
import { useAuthStore } from '@/lib/store/auth-store';
import { formatDistanceToNow } from 'date-fns';
import { enUS, zhCN } from 'date-fns/locale';
import { useLocale } from '@/hooks/use-locale';

// Import gluestack-ui components
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Input } from '@/components/ui/input';
import { InputField } from '@/components/ui/input';
import { Button } from '@/components/ui/m3e-button';
import { ButtonIcon } from '@/components/ui/m3e-button';
import { ButtonText } from '@/components/ui/m3e-button';
import { Spinner } from '@/components/ui/spinner';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';
import { Center } from '@/components/ui/center';
import { Pressable } from '@/components/ui/pressable';

interface BranchCommentsProps {
  comments: BranchComment[];
  isLoading: boolean;
  isAddingComment: boolean;
  onAddComment: (content: string) => Promise<BranchComment | null>;
  onDeleteComment: (commentId: string) => Promise<boolean>;
  onRefresh: () => void;
}

export default function BranchComments({
  comments,
  isLoading,
  isAddingComment,
  onAddComment,
  onDeleteComment,
  onRefresh,
}: BranchCommentsProps) {
  const { t } = useTranslation();
  const { user } = useAuthStore();
  const { locale } = useLocale();

  const [commentText, setCommentText] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  // 处理添加评论
  const handleAddComment = async () => {
    if (!commentText.trim()) return;

    const result = await onAddComment(commentText);
    if (result) {
      setCommentText('');
    }
  };

  // 处理删除评论
  const handleDeleteComment = (commentId: string) => {
    Alert.alert(
      t('storyDetail.deleteCommentTitle', 'Delete Comment'),
      t(
        'storyDetail.deleteCommentConfirm',
        'Are you sure you want to delete this comment?'
      ),
      [
        {
          text: t('cancel', 'Cancel'),
          style: 'cancel',
        },
        {
          text: t('delete', 'Delete'),
          style: 'destructive',
          onPress: async () => {
            await onDeleteComment(commentId);
          },
        },
      ]
    );
  };

  // 处理刷新
  const handleRefresh = async () => {
    setRefreshing(true);
    await onRefresh();
    setRefreshing(false);
  };

  // 格式化时间
  const formatTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, {
        addSuffix: true,
        locale: locale === 'zh' ? zhCN : enUS,
      });
    } catch (error) {
      return dateString;
    }
  };

  // 渲染评论项
  const renderCommentItem = ({ item }: { item: BranchComment }) => {
    const isAuthor = user?.id === item.user_id;

    return (
      <Box className="mb-4 p-4 bg-surface-50 dark:bg-surface-900 rounded-md border border-outline-200 dark:border-outline-700">
        <HStack className="items-center mb-1">
          <Text className="text-sm font-bold text-typography-900 dark:text-typography-100 mr-2">
            {item.profiles?.username ||
              t('storyDetail.unknownAuthor', 'Unknown')}
          </Text>
          <Text className="text-xs text-secondary-500 dark:text-secondary-400 flex-1">
            {formatTime(item.created_at)}
          </Text>
          {isAuthor && (
            <Pressable
              className="p-1"
              onPress={() => handleDeleteComment(item.id)}
            >
              <Trash2
                size={16}
                className="text-error-500 dark:text-error-400"
              />
            </Pressable>
          )}
        </HStack>
        <Text className="text-sm text-typography-800 dark:text-typography-200 leading-5">
          {item.content}
        </Text>
      </Box>
    );
  };

  // 渲染评论列表
  const renderCommentList = () => {
    if (isLoading) {
      return (
        <Center className="p-6">
          <Spinner size="small" color="$primary500" />
          <Text className="mt-2 text-sm text-secondary-500 dark:text-secondary-400 text-center">
            {t('storyDetail.loadingComments', 'Loading comments...')}
          </Text>
        </Center>
      );
    }

    if (comments.length === 0) {
      return (
        <Center className="p-6">
          <MessageCircle
            size={24}
            className="text-secondary-500 dark:text-secondary-400 mb-2"
          />
          <Text className="text-sm text-secondary-500 dark:text-secondary-400 text-center">
            {t(
              'storyDetail.noComments',
              'No comments yet. Be the first to comment!'
            )}
          </Text>
        </Center>
      );
    }

    return (
      <FlatList
        data={comments}
        renderItem={renderCommentItem}
        keyExtractor={(item) => item.id}
        contentContainerClassName="p-4"
        onRefresh={handleRefresh}
        refreshing={refreshing}
      />
    );
  };

  return (
    <Box className="bg-background-50 dark:bg-background-900 rounded-md border border-outline-200 dark:border-outline-700 overflow-hidden my-4">
      <Box className="p-4 border-b border-outline-200 dark:border-outline-700 bg-surface-50 dark:bg-surface-900">
        <Text className="text-base font-bold text-typography-900 dark:text-typography-100">
          {t('storyDetail.comments', 'Comments')} ({comments.length})
        </Text>
      </Box>

      {renderCommentList()}

      {user && (
        <HStack className="p-4 items-center border-t border-outline-200 dark:border-outline-700">
          <Input
            className="flex-1 bg-background-50 dark:bg-background-900 rounded-md border border-outline-200 dark:border-outline-700 mr-2"
            size="md"
          >
            <InputField
              placeholder={t(
                'storyDetail.addCommentPlaceholder',
                'Add a comment...'
              )}
              value={commentText}
              onChangeText={setCommentText}
              multiline
              maxLength={500}
              editable={!isAddingComment}
              className="text-typography-900 dark:text-typography-100 min-h-[80px] py-2"
            />
          </Input>
          <Button
            size="md"
            variant="solid"
            action="primary"
            className="h-10 w-10 rounded-full justify-center items-center"
            onPress={handleAddComment}
            isDisabled={!commentText.trim() || isAddingComment}
          >
            {isAddingComment ? (
              <Spinner size="small" color="$white" />
            ) : (
              <ButtonIcon as={Send} size="sm" />
            )}
          </Button>
        </HStack>
      )}

      {!user && (
        <Box className="p-4 border-t border-outline-200 dark:border-outline-700 bg-surface-50 dark:bg-surface-900 items-center">
          <Text className="text-sm text-secondary-500 dark:text-secondary-400">
            {t('storyDetail.loginToComment', 'Please log in to add a comment')}
          </Text>
        </Box>
      )}
    </Box>
  );
}
