import { StorySegment } from '../types';
import { BranchNode } from './types';

/**
 * 从平面段落列表构建分支树
 *
 * @param segments 段落列表
 * @returns 分支树的根节点
 */
export function buildBranchTree(segments: StorySegment[]): BranchNode | null {
  if (!segments || segments.length === 0) {
    return null;
  }

  // 创建段落ID到段落的映射
  const segmentMap = new Map<string, StorySegment>();
  segments.forEach((segment) => {
    segmentMap.set(segment.id, segment);
  });

  // 创建子节点映射
  const childrenMap = new Map<string | null, string[]>();
  segments.forEach((segment) => {
    const parentId = segment.parent_segment_id || null;
    if (!childrenMap.has(parentId)) {
      childrenMap.set(parentId, []);
    }
    childrenMap.get(parentId)?.push(segment.id);
  });

  // 查找根节点（没有父节点的段落）
  const rootSegmentId = childrenMap.get(null)?.[0];
  if (!rootSegmentId) {
    return null;
  }

  // 递归构建树
  function buildNode(
    segmentId: string,
    level: number,
    path: string[]
  ): BranchNode {
    const segment = segmentMap.get(segmentId)!;
    const childIds = childrenMap.get(segmentId) || [];
    const children = childIds
      .map((childId) => buildNode(childId, level + 1, [...path, segmentId]))
      .sort((a, b) => {
        // 按order_in_branch和created_at排序
        if (a.segment.order_in_branch !== b.segment.order_in_branch) {
          return a.segment.order_in_branch - b.segment.order_in_branch;
        }
        return (
          new Date(a.segment.created_at).getTime() -
          new Date(b.segment.created_at).getTime()
        );
      });

    return {
      id: segmentId,
      segment,
      children,
      level,
      path,
      // 这里可以添加分支标题逻辑，如果数据库中有metadata字段
      // branchTitle: segment.metadata?.branchTitle,
    };
  }

  return buildNode(rootSegmentId, 0, []);
}
