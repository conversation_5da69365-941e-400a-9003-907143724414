import React from 'react';
import { Image } from 'react-native';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';

interface Story {
  id: string;
  title: string;
  coverImage?: string;
}

interface StoryPreviewCardProps {
  story: Story;
  onPress?: (storyId: string) => void;
}

export function StoryPreviewCard({ story, onPress }: StoryPreviewCardProps) {
  const handlePress = () => {
    if (onPress) {
      onPress(story.id);
    }
  };

  return (
    <Pressable 
      className="mr-4 w-[150px]"
      onPress={handlePress}
    >
      <Image
        source={{ uri: story.coverImage || 'https://via.placeholder.com/150x100' }}
        style={{ 
          width: 150, 
          height: 100, 
          borderRadius: 8,
          marginBottom: 8,
          backgroundColor: '#e5e5e5' // Placeholder background
        }}
        resizeMode="cover"
      />
      <Text 
        className="text-sm font-medium text-typography-900 dark:text-typography-100 leading-[18px]"
        numberOfLines={2}
      >
        {story.title}
      </Text>
    </Pressable>
  );
}
