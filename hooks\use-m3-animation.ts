import { useRef, useCallback, useEffect } from 'react';
import { Animated, Vibration, Platform } from 'react-native';
import {
  M3AnimationType,
  M3AnimationConfig,
  M3AnimationUtils,
  M3ExpressiveAnimations,
  M3ExpressiveDuration,
} from '@/lib/animation/material3-expressive-animations';

export interface UseM3AnimationOptions {
  initialValue?: number;
  autoStart?: boolean;
  enableHaptics?: boolean;
  onComplete?: () => void;
  onStart?: () => void;
}

export interface M3AnimationControls {
  value: Animated.Value;
  start: (config?: Partial<M3AnimationConfig>) => void;
  stop: () => void;
  reset: (newValue?: number) => void;
  isAnimating: boolean;
}

/**
 * Material 3 Expressive动画Hook
 * 提供简单易用的动画控制接口
 */
export function useM3Animation(
  type: M3AnimationType,
  options: UseM3AnimationOptions = {}
): M3AnimationControls {
  const {
    initialValue = 0,
    autoStart = false,
    enableHaptics = false,
    onComplete,
    onStart,
  } = options;

  const animatedValue = useRef(
    M3AnimationUtils.createValue(initialValue)
  ).current;
  const isAnimating = useRef(false);
  const currentAnimation = useRef<Animated.CompositeAnimation | null>(null);

  // 触觉反馈函数
  const triggerHapticFeedback = useCallback(() => {
    if (enableHaptics && Platform.OS !== 'web') {
      // 根据动画类型选择不同的触觉反馈
      switch (type) {
        case 'spring':
        case 'bounce':
        case 'elastic':
          Vibration.vibrate(50); // 短震动
          break;
        case 'buttonPress':
          Vibration.vibrate(30); // 轻微震动
          break;
        default:
          Vibration.vibrate(20); // 最轻震动
      }
    }
  }, [enableHaptics, type]);

  // 开始动画
  const start = useCallback(
    (config: Partial<M3AnimationConfig> = {}) => {
      if (isAnimating.current) {
        currentAnimation.current?.stop();
      }

      // 获取预设动画配置
      const presetConfig = M3ExpressiveAnimations[
        type as keyof typeof M3ExpressiveAnimations
      ] || {
        duration: M3ExpressiveDuration.standard,
        useNativeDriver: true,
      };

      // 合并配置
      const finalConfig = { ...presetConfig, ...config };

      let animation: Animated.CompositeAnimation;

      // 根据动画类型创建不同的动画
      switch (type) {
        case 'fadeIn':
          animation = M3AnimationUtils.createSpringAnimation(
            animatedValue,
            1,
            finalConfig
          );
          break;

        case 'fadeOut':
          animation = M3AnimationUtils.createSpringAnimation(
            animatedValue,
            0,
            finalConfig
          );
          break;

        case 'scale':
        case 'spring':
        case 'elastic':
          animation = M3AnimationUtils.createSpringAnimation(
            animatedValue,
            1.1,
            finalConfig
          );
          break;

        case 'slideUp':
          animation = M3AnimationUtils.createSpringAnimation(
            animatedValue,
            -100,
            finalConfig
          );
          break;

        case 'slideDown':
          animation = M3AnimationUtils.createSpringAnimation(
            animatedValue,
            100,
            finalConfig
          );
          break;

        case 'slideLeft':
          animation = M3AnimationUtils.createSpringAnimation(
            animatedValue,
            -100,
            finalConfig
          );
          break;

        case 'slideRight':
          animation = M3AnimationUtils.createSpringAnimation(
            animatedValue,
            100,
            finalConfig
          );
          break;

        case 'bounce':
          // 创建弹跳序列动画
          animation = M3AnimationUtils.createSequence([
            M3AnimationUtils.createSpringAnimation(animatedValue, 1.2, {
              ...finalConfig,
              duration: finalConfig.duration ? finalConfig.duration / 3 : 100,
            }),
            M3AnimationUtils.createSpringAnimation(animatedValue, 0.9, {
              ...finalConfig,
              duration: finalConfig.duration ? finalConfig.duration / 3 : 100,
            }),
            M3AnimationUtils.createSpringAnimation(animatedValue, 1, {
              ...finalConfig,
              duration: finalConfig.duration ? finalConfig.duration / 3 : 100,
            }),
          ]);
          break;

        case 'shimmer':
          // 创建闪光效果
          animation = M3AnimationUtils.createLoop(
            M3AnimationUtils.createSequence([
              M3AnimationUtils.createSpringAnimation(
                animatedValue,
                1,
                finalConfig
              ),
              M3AnimationUtils.createSpringAnimation(
                animatedValue,
                0.3,
                finalConfig
              ),
            ]),
            finalConfig.iterations || -1
          );
          break;

        default:
          animation = M3AnimationUtils.createSpringAnimation(
            animatedValue,
            1,
            finalConfig
          );
      }

      currentAnimation.current = animation;
      isAnimating.current = true;

      // 触发触觉反馈
      if (finalConfig.enableHaptics) {
        triggerHapticFeedback();
      }

      // 动画开始回调
      onStart?.();

      // 启动动画
      animation.start(({ finished }) => {
        isAnimating.current = false;
        if (finished) {
          onComplete?.();
        }
      });
    },
    [type, animatedValue, triggerHapticFeedback, onComplete, onStart]
  );

  // 停止动画
  const stop = useCallback(() => {
    currentAnimation.current?.stop();
    isAnimating.current = false;
  }, []);

  // 重置动画
  const reset = useCallback(
    (newValue?: number) => {
      stop();
      animatedValue.setValue(newValue ?? initialValue);
    },
    [animatedValue, initialValue, stop]
  );

  // 自动启动动画
  useEffect(() => {
    if (autoStart) {
      start();
    }
  }, [autoStart, start]);

  return {
    value: animatedValue,
    start,
    stop,
    reset,
    isAnimating: isAnimating.current,
  };
}

/**
 * 预设的常用动画Hook
 */
export const useM3ButtonAnimation = (options?: UseM3AnimationOptions) => {
  return useM3Animation('spring', { enableHaptics: true, ...options });
};

export const useM3FadeAnimation = (options?: UseM3AnimationOptions) => {
  return useM3Animation('fadeIn', options);
};

export const useM3ScaleAnimation = (options?: UseM3AnimationOptions) => {
  return useM3Animation('scale', options);
};

export const useM3SlideAnimation = (
  direction: 'up' | 'down' | 'left' | 'right' = 'up',
  options?: UseM3AnimationOptions
) => {
  const type: M3AnimationType = `slide${
    direction.charAt(0).toUpperCase() + direction.slice(1)
  }` as M3AnimationType;
  return useM3Animation(type, options);
};

export const useM3ShimmerAnimation = (options?: UseM3AnimationOptions) => {
  return useM3Animation('shimmer', { autoStart: true, ...options });
};

export const useM3ElasticAnimation = (options?: UseM3AnimationOptions) => {
  return useM3Animation('elastic', { enableHaptics: true, ...options });
};

/**
 * 复合动画Hook - 同时控制多个动画值
 */
export function useM3CompoundAnimation(
  configs: Array<{ type: M3AnimationType; options?: UseM3AnimationOptions }>
) {
  const animations = configs.map(({ type, options }) =>
    useM3Animation(type, options)
  );

  const startAll = useCallback(
    (globalConfig?: Partial<M3AnimationConfig>) => {
      animations.forEach((animation) => animation.start(globalConfig));
    },
    [animations]
  );

  const stopAll = useCallback(() => {
    animations.forEach((animation) => animation.stop());
  }, [animations]);

  const resetAll = useCallback(() => {
    animations.forEach((animation) => animation.reset());
  }, [animations]);

  return {
    animations,
    startAll,
    stopAll,
    resetAll,
  };
}

export default useM3Animation;
