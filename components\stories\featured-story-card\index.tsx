import React from 'react';
import { ImageBackground } from 'react-native';
import { Story as ApiStory } from '@/api/stories';
import { FeaturedStoryCardHeader } from './featured-story-card-header';
import { FeaturedStoryCardStats } from './featured-story-card-stats';
import { FeaturedStoryCardContent } from './featured-story-card-content';
import { FeaturedStoryCardFooter } from './featured-story-card-footer';
import { Pressable } from '@/components/ui/pressable';
import { Box } from '@/components/ui/box';

interface FeaturedStoryCardProps {
  story: ApiStory;
  onPress?: () => void;
  className?: string;
}

export default function FeaturedStoryCard({
  story,
  onPress,
  className = '',
}: FeaturedStoryCardProps) {
  const coverImageUrl = story.cover_image_url;
  const isPremium = false;
  const authorName = story.profiles?.username || 'Unknown';
  const views = 0;
  const likes = story.like_count || 0;
  const summary = story.first_segment_content || '';
  const themeTags = story.tags || [];

  return (
    <Pressable
      className={`rounded-xl overflow-hidden shadow-md mb-4 ${className}`}
      onPress={onPress}
    >
      <ImageBackground
        source={
          coverImageUrl
            ? { uri: coverImageUrl }
            : require('../../../assets/images/default-story-placeholder.png')
        }
        style={{ height: 250 }}
        imageStyle={{ borderRadius: 12 }}
      >
        <Box className="bg-black/60 h-full px-4 py-6 justify-end">
          <Box className="relative">
            <FeaturedStoryCardHeader
              title={story.title}
              isPremium={isPremium}
            />

            <FeaturedStoryCardStats
              authorName={authorName}
              views={views}
              likes={likes}
            />

            <FeaturedStoryCardContent summary={summary} themeTags={themeTags} />

            <FeaturedStoryCardFooter onPress={onPress} />
          </Box>
        </Box>
      </ImageBackground>
    </Pressable>
  );
}
