import { Profile } from '../profiles';

export type NotificationType = 
  | 'like'       // 点赞通知
  | 'comment'    // 评论通知
  | 'follow'     // 关注通知
  | 'mention'    // 提及通知
  | 'new_story'  // 新故事通知
  | 'new_segment' // 新段落通知
  | 'system';    // 系统通知

export interface Notification {
  id: string;
  user_id: string;
  type: NotificationType;
  title: string;
  body: string;
  data?: {
    story_id?: string;
    segment_id?: string;
    comment_id?: string;
    [key: string]: any;
  } | null;
  is_read: boolean;
  created_at: string;
  
  // 关联数据
  actor_id?: string | null;
  story_id?: string | null;
  segment_id?: string | null;
  
  // 嵌入的关联数据
  actor?: Pick<Profile, 'id' | 'username' | 'avatar_url'> | null;
}

export interface GetNotificationsOptions {
  limit?: number;
  offset?: number;
  types?: NotificationType[];
  is_read?: boolean;
}

export interface NotificationSettings {
  push_enabled: boolean;
  email_enabled: boolean;
  like_notifications: boolean;
  comment_notifications: boolean;
  follow_notifications: boolean;
  mention_notifications: boolean;
  new_story_notifications: boolean;
  new_segment_notifications: boolean;
  system_notifications: boolean;
}