import { Profile } from '../profiles'; // Used for embedding author info

// --- Core Type Definitions (based on new DB schema) ---

export interface Story {
  id: string;
  author_id: string;
  title: string;
  status: 'draft' | 'published' | 'archived';
  visibility: 'public' | 'private' | 'unlisted';
  tags?: string[] | null;
  cover_image_url?: string | null;
  created_at: string;
  updated_at: string;
  // Embedded/joined data
  profiles?: Pick<Profile, 'username' | 'avatar_url' | 'id'> | null; // Author info
  like_count?: number;
  is_liked_by_user?: boolean;
  first_segment_content?: string | null; // For story previews
}

export interface StorySegment {
  id: string;
  story_id: string;
  author_id: string;
  content_type: 'text' | 'image_url' | 'audio_url';
  content: string;
  parent_segment_id?: string | null;
  order_in_branch: number; // For ordering segments under the same parent if created_at is not enough
  is_ai_generated: boolean;
  created_at: string;
  updated_at: string;
  // Embedded/joined data
  profiles?: Pick<Profile, 'username' | 'avatar_url' | 'id'> | null; // Segment author info
  // Counts
  children_count?: number; // Number of child segments (branches)
  comment_count?: number; // Number of comments
  likes_count?: number; // Number of likes
  dislikes_count?: number; // Number of dislikes
  bookmarks_count?: number; // Number of bookmarks
}

export interface StoryWithSegments extends Story {
  story_segments: StorySegment[]; // All segments of the story, ordered
}

export type GetStoriesOptions = {
  limit?: number;
  offset?: number;
  authorId?: string; // To get stories by a specific author
  filter?:
    | 'latest'
    | 'popular'
    | 'following'
    | 'recommended'
    | 'my_drafts'
    | 'my_published';
  searchTerm?: string;
  tags?: string[]; // Filter by tags
  sortBy?: 'created_at' | 'updated_at' | 'likes_count' | 'views_count'; // 排序字段
  sortOrder?: 'asc' | 'desc'; // 排序方向
};
