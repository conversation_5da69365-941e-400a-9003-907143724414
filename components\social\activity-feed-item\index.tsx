import React from 'react';
import { MessageSquare, Heart, GitBranch } from 'lucide-react-native';
import { User } from '@/types/user';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Image } from '@/components/ui/image';
import { Pressable } from '@/components/ui/pressable';

interface ActivityItem {
  id: string;
  type: 'comment' | 'like' | 'branch';
  user: User;
  storyTitle: string;
  content: string;
  time: string;
}

interface ActivityFeedItemProps {
  item: ActivityItem;
  onPress?: () => void;
}

export default function ActivityFeedItem({
  item,
  onPress,
}: ActivityFeedItemProps) {
  const renderIcon = () => {
    const color = getIconColor();
    const size = 16;

    switch (item.type) {
      case 'comment':
        return <MessageSquare size={size} color={color} />;
      case 'like':
        return <Heart size={size} color={color} fill={color} />;
      case 'branch':
        return <GitBranch size={size} color={color} />;
      default:
        return null;
    }
  };

  const getIconColor = () => {
    switch (item.type) {
      case 'comment':
        return '#333333'; // primary-500
      case 'like':
        return '#FF4D4D';
      case 'branch':
        return '#30D158';
      default:
        return '#333333'; // typography-900
    }
  };

  return (
    <Pressable
      className="flex-row p-4 rounded-lg border border-outline-300 bg-background-0"
      onPress={onPress}
      disabled={!onPress}
    >
      <Image
        source={{ uri: item.user.avatar }}
        className="w-10 h-10 rounded-full mr-4"
        alt={item.user.displayName}
      />

      <Box className="flex-1">
        <Box className="flex-row justify-between items-center mb-1">
          <Text className="font-bold text-sm text-typography-900">
            {item.user.displayName}
          </Text>
          <Text className="text-xs text-typography-600">{item.time}</Text>
        </Box>

        <Box className="flex-row items-center mb-1 gap-1">
          {renderIcon()}
          <Text
            className="text-sm text-typography-600 flex-shrink-1"
            numberOfLines={1}
          >
            {item.content}
          </Text>
        </Box>

        <Text
          className="text-sm font-medium text-primary-500"
          numberOfLines={1}
        >
          《{item.storyTitle}》
        </Text>
      </Box>
    </Pressable>
  );
}
