import React from 'react';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Input, InputField } from '@/components/ui/input';
import { HStack } from '@/components/ui/hstack';
import { Bookmark } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';

interface TitleInputProps {
  title: string;
  onTitleChange: (text: string) => void;
}

export default function TitleInput({ title, onTitleChange }: TitleInputProps) {
  const { t } = useTranslation();

  return (
    <Box className="mb-6">
      <HStack className="items-center mb-2">
        <Bookmark size={18} className="text-primary-500" />
        <Text
          size="md"
          className="font-medium text-typography-900 dark:text-typography-50 ml-2"
        >
          {t('storyForm.titleLabel', '标题')}
        </Text>
      </HStack>
      <Input
        variant="outline"
        size="md"
        className={`rounded-md ${
          title.length > 0
            ? 'border-primary-500'
            : 'border-background-300 dark:border-background-700'
        }`}
      >
        <InputField
          value={title}
          onChangeText={onTitleChange}
          placeholder={t('storyForm.titlePlaceholder', '输入故事标题')}
          maxLength={100}
        />
      </Input>
      {title.length > 0 && (
        <Text size="xs" className="text-typography-500 self-end mt-1">
          {title.length}/100
        </Text>
      )}
    </Box>
  );
}
