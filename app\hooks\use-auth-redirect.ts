import { useEffect } from 'react';
import { useRouter, useSegments } from 'expo-router';
import { useAuthStore } from '@/lib/store/auth-store';

interface UseAuthRedirectProps {
  fontsLoaded: boolean;
  fontError: Error | null;
  settingsHasHydrated: boolean;
  authHasHydrated: boolean;
}

export default function useAuthRedirect({
  fontsLoaded,
  fontError,
  settingsHasHydrated,
  authHasHydrated,
}: UseAuthRedirectProps) {
  const router = useRouter();
  const segments = useSegments();
  const { session, isInitialized: authIsInitialized } = useAuthStore();

  useEffect(() => {
    // Ensure everything is initialized before making routing decisions
    if (
      (!fontsLoaded && !fontError) ||
      !settingsHasHydrated ||
      !authHasHydrated ||
      !authIsInitialized
    ) {
      return; // Still loading critical assets or initializing auth
    }

    const inAuthGroup = segments[0] === '(auth)';

    // If user is logged in (session exists)
    if (session) {
      if (inAuthGroup) {
        // User is logged in and trying to access an auth screen (e.g., login, register)
        // Redirect them to the main app area (e.g., home tab)
        console.log(
          '[AuthGuard] User logged in, in auth group, redirecting to /home'
        );
        router.replace('/(tabs)/home');
      }
      // If user is logged in and NOT in auth group, they are in the right place (main app), do nothing.
    } else {
      // User is not logged in (no session)
      if (!inAuthGroup) {
        // User is not logged in and trying to access a protected screen (not in auth group)
        // Redirect them to the login screen
        // Ensure the target is not a screen that itself causes a loop (e.g. if / is the login screen)
        const currentRoute = segments.join('/');
        if (currentRoute && currentRoute !== '' && currentRoute !== '/') {
          // Avoid redirecting from initial empty route or root if it's a public landing
          console.log(
            '[AuthGuard] User not logged in, not in auth group, redirecting to /login. Current segments:',
            segments
          );
          router.replace('/(auth)/login');
        }
      }
      // If user is not logged in and IS in auth group, they are in the right place (login/register), do nothing.
    }
  }, [
    session,
    authIsInitialized,
    fontsLoaded,
    fontError,
    settingsHasHydrated,
    authHasHydrated,
    segments,
    router,
  ]);

  return { authIsInitialized };
}
