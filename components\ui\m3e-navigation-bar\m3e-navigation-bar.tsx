import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { M3EBadge } from '../m3e-badge';

// Navigation Bar Item 的属性接口
export interface M3ENavigationBarItemProps {
  /** 图标组件 */
  icon: React.ReactNode;
  /** 选中状态的图标组件 */
  selectedIcon?: React.ReactNode;
  /** 标签文本 */
  label?: string;
  /** 是否选中 */
  selected?: boolean;
  /** 是否显示标签 */
  showLabel?: boolean;
  /** 徽章配置 */
  badge?: {
    label?: string | number;
    size?: 'small' | 'large';
    visible?: boolean;
  };
  /** 点击事件 */
  onPress?: () => void;
  /** 自定义样式类名 */
  className?: string;
}

// Navigation Bar 的属性接口
export interface M3ENavigationBarProps {
  /** 导航项列表 */
  items: M3ENavigationBarItemProps[];
  /** 当前选中的索引 */
  selectedIndex?: number;
  /** 是否显示标签 */
  showLabels?: boolean;
  /** 导航栏布局方向 */
  orientation?: 'horizontal' | 'vertical';
  /** 自定义样式类名 */
  className?: string;
  /** 选择变化事件 */
  onSelectionChange?: (index: number) => void;
}

// 获取容器样式
const getBarContainerClasses = (orientation: string) => {
  const orientationClasses = {
    horizontal:
      'flex-row bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700',
    vertical:
      'flex-col bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700',
  };

  return (
    orientationClasses[orientation as keyof typeof orientationClasses] ||
    orientationClasses.horizontal
  );
};

// 获取导航项样式
const getNavItemClasses = (orientation: string) => {
  const orientationClasses = {
    horizontal: 'flex-1 items-center justify-center py-2 px-1',
    vertical: 'items-center justify-center py-3 px-2',
  };

  return (
    orientationClasses[orientation as keyof typeof orientationClasses] ||
    orientationClasses.horizontal
  );
};

// 获取图标容器样式
const getIconContainerClasses = (selected: boolean) => {
  return selected
    ? 'bg-purple-100 dark:bg-purple-900 rounded-2xl'
    : 'bg-transparent';
};

/**
 * M3E Navigation Bar Item 组件
 */
export const M3ENavigationBarItem: React.FC<
  M3ENavigationBarItemProps & {
    orientation: M3ENavigationBarProps['orientation'];
  }
> = ({
  icon,
  selectedIcon,
  label,
  selected = false,
  showLabel = true,
  badge,
  onPress,
  orientation = 'horizontal',
  className = '',
}) => {
  const displayIcon = selected && selectedIcon ? selectedIcon : icon;

  return (
    <TouchableOpacity
      onPress={onPress}
      className={`${getNavItemClasses(orientation)} ${className}`}
      activeOpacity={0.7}
    >
      <View className="relative">
        <View
          className={`${
            orientation === 'horizontal'
              ? 'w-16 h-8 items-center justify-center'
              : 'w-14 h-8 items-center justify-center'
          } relative ${getIconContainerClasses(selected)}`}
        >
          {displayIcon}
          {badge && badge.visible && (
            <M3EBadge
              label={badge.label}
              size={badge.size}
              className="absolute -top-1 -right-1"
            />
          )}
        </View>
      </View>

      {showLabel && label && (
        <Text
          className={`text-xs font-medium text-center mt-1 ${
            selected
              ? 'text-gray-900 dark:text-white'
              : 'text-gray-600 dark:text-gray-400'
          }`}
        >
          {label}
        </Text>
      )}
    </TouchableOpacity>
  );
};

/**
 * M3E Navigation Bar 组件
 *
 * 基于 Material Design 3 规范的底部导航栏组件，提供主要导航功能。
 *
 * @example
 * ```tsx
 * const navigationItems = [
 *   {
 *     icon: <Icon name="home" />,
 *     selectedIcon: <Icon name="home-filled" />,
 *     label: "首页",
 *     badge: { label: "3", visible: true }
 *   },
 *   {
 *     icon: <Icon name="search" />,
 *     label: "搜索"
 *   }
 * ];
 *
 * <M3ENavigationBar
 *   items={navigationItems}
 *   selectedIndex={0}
 *   orientation="horizontal"
 *   onSelectionChange={(index) => console.log('Selected:', index)}
 * />
 * ```
 */
export const M3ENavigationBar: React.FC<M3ENavigationBarProps> = ({
  items,
  selectedIndex = 0,
  showLabels = true,
  orientation = 'horizontal',
  className = '',
  onSelectionChange,
}) => {
  const baseClasses = orientation === 'horizontal' ? 'h-16 px-2' : 'w-20 py-2';
  const combinedClasses = `${baseClasses} ${className}`;

  return (
    <View
      className={`${combinedClasses} ${getBarContainerClasses(orientation)}`}
    >
      {items.map((item, index) => (
        <M3ENavigationBarItem
          key={index}
          {...item}
          selected={index === selectedIndex}
          showLabel={showLabels}
          orientation={orientation}
          onPress={() => {
            item.onPress?.();
            onSelectionChange?.(index);
          }}
        />
      ))}
    </View>
  );
};

export default M3ENavigationBar;
