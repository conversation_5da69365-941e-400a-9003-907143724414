import React from 'react';
import { KeyboardAvoidingView, Platform } from 'react-native';
import { Stack, useLocalSearchParams } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { ChevronLeft } from 'lucide-react-native';
import { useSegmentCreation } from '../hooks/use-segment-creation';
import SegmentForm from '../components/segment-form';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/m3e-button';
import { ScrollView } from '@/components/ui/scroll-view';
import { SafeAreaView } from '@/components/ui/safe-area-view';

export default function CreateSegmentScreen() {
  const { t } = useTranslation();
  const { storyId, parentSegmentId } = useLocalSearchParams<{
    storyId?: string;
    parentSegmentId?: string;
  }>();

  const {
    content,
    setContent,
    title,
    setTitle,
    isSubmitting,
    error,
    isAIGenerated,
    setIsAIGenerated,
    handleSubmit,
    handleCancel,
  } = useSegmentCreation({
    storyId,
    parentSegmentId,
  });

  return (
    <SafeAreaView className="flex-1 bg-background-50 dark:bg-background-900">
      <Stack.Screen options={{ headerShown: false }} />

      <Box className="flex-row items-center justify-between px-4 py-4 border-b border-outline-200 dark:border-outline-700 bg-background-100 dark:bg-background-800">
        <Button className="p-1" variant="link" onPress={handleCancel}>
          <ChevronLeft
            size={24}
            color="currentColor"
            className="text-typography-900 dark:text-typography-100"
          />
        </Button>
        <Text className="text-lg font-medium text-typography-900 dark:text-typography-100">
          {t('createSegment.title', 'Create New Segment')}
        </Text>
        <Box className="w-6" />
      </Box>

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <ScrollView className="flex-1 p-4">
          <SegmentForm
            content={content}
            setContent={setContent}
            title={title}
            setTitle={setTitle}
            isSubmitting={isSubmitting}
            error={error}
            isAIGenerated={isAIGenerated}
            setIsAIGenerated={setIsAIGenerated}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}
