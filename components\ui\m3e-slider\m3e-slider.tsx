import React, { useState, useCallback } from 'react';
import { View, PanGestureHandler, PanGestureHandlerGestureEvent } from 'react-native';
import { runOnJS } from 'react-native-reanimated';

/**
 * M3E Slider 组件的属性接口
 */
export interface M3ESliderProps {
  /** 滑块的最小值 */
  minimumValue?: number;
  /** 滑块的最大值 */
  maximumValue?: number;
  /** 当前值 */
  value?: number;
  /** 值变化回调 */
  onValueChange?: (value: number) => void;
  /** 滑块方向 */
  orientation?: 'horizontal' | 'vertical';
  /** 滑块尺寸 */
  size?: 'xsmall' | 'small' | 'medium' | 'large' | 'xlarge';
  /** 是否禁用 */
  disabled?: boolean;
  /** 是否显示值指示器 */
  showValueIndicator?: boolean;
  /** 是否显示刻度 */
  showStops?: boolean;
  /** 刻度数量 */
  stopCount?: number;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * 获取滑块尺寸相关的样式
 */
const getSliderSizes = (size: string, orientation: string) => {
  const sizes = {
    xsmall: {
      horizontal: { track: 'h-1', handle: 'w-4 h-4', container: 'h-12' },
      vertical: { track: 'w-1', handle: 'w-4 h-4', container: 'w-12' }
    },
    small: {
      horizontal: { track: 'h-1', handle: 'w-5 h-5', container: 'h-14' },
      vertical: { track: 'w-1', handle: 'w-5 h-5', container: 'w-14' }
    },
    medium: {
      horizontal: { track: 'h-1', handle: 'w-6 h-6', container: 'h-16' },
      vertical: { track: 'w-1', handle: 'w-6 h-6', container: 'w-16' }
    },
    large: {
      horizontal: { track: 'h-1', handle: 'w-7 h-7', container: 'h-18' },
      vertical: { track: 'w-1', handle: 'w-7 h-7', container: 'w-18' }
    },
    xlarge: {
      horizontal: { track: 'h-1', handle: 'w-8 h-8', container: 'h-20' },
      vertical: { track: 'w-1', handle: 'w-8 h-8', container: 'w-20' }
    }
  };
  
  return sizes[size as keyof typeof sizes]?.[orientation as keyof typeof sizes.xsmall] || sizes.medium.horizontal;
};

/**
 * 获取滑块的样式类
 */
const getSliderStyles = (disabled: boolean) => {
  return {
    track: disabled 
      ? 'bg-gray-300 dark:bg-gray-600' 
      : 'bg-gray-300 dark:bg-gray-600',
    activeTrack: disabled 
      ? 'bg-gray-400 dark:bg-gray-500' 
      : 'bg-purple-600 dark:bg-purple-400',
    handle: disabled 
      ? 'bg-gray-400 border-gray-400' 
      : 'bg-purple-600 border-purple-600 shadow-md',
    container: 'relative flex items-center justify-center'
  };
};

/**
 * M3E Slider 组件
 * 
 * 基于 Material Design 3 规范的滑块组件，支持水平和垂直方向，多种尺寸和状态。
 * 
 * @example
 * ```tsx
 * const [value, setValue] = useState(50);
 * 
 * <M3ESlider
 *   value={value}
 *   onValueChange={setValue}
 *   minimumValue={0}
 *   maximumValue={100}
 *   orientation="horizontal"
 *   size="medium"
 *   showValueIndicator={true}
 * />
 * ```
 */
export const M3ESlider: React.FC<M3ESliderProps> = ({
  minimumValue = 0,
  maximumValue = 100,
  value = 0,
  onValueChange,
  orientation = 'horizontal',
  size = 'medium',
  disabled = false,
  showValueIndicator = false,
  showStops = false,
  stopCount = 5,
  className = '',
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [localValue, setLocalValue] = useState(value);

  const sizes = getSliderSizes(size, orientation);
  const styles = getSliderStyles(disabled);

  // 计算值的百分比位置
  const valuePercentage = ((localValue - minimumValue) / (maximumValue - minimumValue)) * 100;

  const handleValueChange = useCallback((newValue: number) => {
    const clampedValue = Math.max(minimumValue, Math.min(maximumValue, newValue));
    setLocalValue(clampedValue);
    onValueChange?.(clampedValue);
  }, [minimumValue, maximumValue, onValueChange]);

  // 渲染刻度
  const renderStops = () => {
    if (!showStops) return null;
    
    const stops = [];
    for (let i = 0; i <= stopCount; i++) {
      const stopValue = minimumValue + (i / stopCount) * (maximumValue - minimumValue);
      const stopPercentage = ((stopValue - minimumValue) / (maximumValue - minimumValue)) * 100;
      
      stops.push(
        <View
          key={i}
          className={`absolute w-1 h-1 bg-gray-400 rounded-full ${
            orientation === 'horizontal' 
              ? `top-1/2 -translate-y-1/2` 
              : `left-1/2 -translate-x-1/2`
          }`}
          style={{
            [orientation === 'horizontal' ? 'left' : 'top']: `${stopPercentage}%`
          }}
        />
      );
    }
    
    return stops;
  };

  // 渲染值指示器
  const renderValueIndicator = () => {
    if (!showValueIndicator || !isDragging) return null;
    
    return (
      <View
        className={`absolute bg-purple-600 text-white px-2 py-1 rounded text-xs ${
          orientation === 'horizontal' 
            ? 'bottom-full mb-2 left-1/2 -translate-x-1/2' 
            : 'right-full mr-2 top-1/2 -translate-y-1/2'
        }`}
        style={{
          [orientation === 'horizontal' ? 'left' : 'top']: `${valuePercentage}%`
        }}
      >
        {Math.round(localValue)}
      </View>
    );
  };

  const containerClasses = `${styles.container} ${sizes.container} ${className}`;
  const trackClasses = orientation === 'horizontal' 
    ? `w-full ${sizes.track} ${styles.track} rounded-full`
    : `h-full ${sizes.track} ${styles.track} rounded-full`;

  return (
    <View className={containerClasses}>
      {/* 背景轨道 */}
      <View className={trackClasses} />
      
      {/* 活动轨道 */}
      <View
        className={`absolute ${styles.activeTrack} rounded-full ${
          orientation === 'horizontal' 
            ? `${sizes.track} left-0` 
            : `${sizes.track} bottom-0`
        }`}
        style={{
          [orientation === 'horizontal' ? 'width' : 'height']: `${valuePercentage}%`
        }}
      />
      
      {/* 刻度 */}
      {renderStops()}
      
      {/* 手柄 */}
      <View
        className={`absolute ${sizes.handle} ${styles.handle} rounded-full border-2 ${
          isDragging ? 'scale-110' : ''
        } transition-transform`}
        style={{
          [orientation === 'horizontal' ? 'left' : 'top']: `${valuePercentage}%`,
          transform: [
            { [orientation === 'horizontal' ? 'translateX' : 'translateY']: '-50%' }
          ]
        }}
      />
      
      {/* 值指示器 */}
      {renderValueIndicator()}
    </View>
  );
};

export default M3ESlider;
