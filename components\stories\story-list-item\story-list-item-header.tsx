import React from 'react';
import { MoreVertical } from 'lucide-react-native';

// Import gluestack-ui components
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { HStack } from '@/components/ui/hstack';

interface StoryListItemHeaderProps {
  title: string;
  onOptionsPress?: () => void;
}

export function StoryListItemHeader({
  title,
  onOptionsPress,
}: StoryListItemHeaderProps) {
  return (
    <HStack className="justify-between">
      <Text
        className="font-bold text-base text-typography-900 dark:text-typography-100"
        numberOfLines={1}
      >
        {title}
      </Text>

      {onOptionsPress && (
        <Pressable onPress={onOptionsPress}>
          <MoreVertical
            size={20}
            className="text-secondary-500 dark:text-secondary-400"
          />
        </Pressable>
      )}
    </HStack>
  );
}
