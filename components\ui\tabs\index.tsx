'use client';
import React, { createContext, useContext, useState } from 'react';
import { Pressable } from '@/components/ui/pressable';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';

// Context for Tabs
interface TabsContextType {
  activeTab: string;
  setActiveTab: (id: string) => void;
}

const TabsContext = createContext<TabsContextType | undefined>(undefined);

function useTabsContext() {
  const context = useContext(TabsContext);
  if (!context) {
    throw new Error('Tabs components must be used within a Tabs provider');
  }
  return context;
}

// Root Tabs component
interface TabsProps {
  defaultValue?: string;
  onChange?: (value: string) => void;
  children: React.ReactNode;
  className?: string;
}

export function Tabs({
  defaultValue,
  onChange,
  children,
  className = '',
}: TabsProps) {
  const [activeTab, setActiveTab] = useState(defaultValue || '');

  const handleTabChange = (id: string) => {
    setActiveTab(id);
    onChange?.(id);
  };

  return (
    <TabsContext.Provider
      value={{ activeTab, setActiveTab: handleTabChange }}
    >
      <Box className={`${className}`}>
        {children}
      </Box>
    </TabsContext.Provider>
  );
}

// TabsList component
interface TabsListProps {
  children: React.ReactNode;
  className?: string;
}

export function TabsList({ children, className = '' }: TabsListProps) {
  return (
    <HStack className={`border-b border-outline-200 dark:border-outline-700 ${className}`}>
      {children}
    </HStack>
  );
}

// TabsTrigger component
interface TabsTriggerProps {
  value: string;
  children: React.ReactNode;
  className?: string;
}

export function TabsTrigger({
  value,
  children,
  className = '',
}: TabsTriggerProps) {
  const { activeTab, setActiveTab } = useTabsContext();
  const isActive = activeTab === value;

  return (
    <Pressable
      className={`py-3 px-4 ${
        isActive
          ? 'border-b-2 border-primary-500'
          : ''
      } ${className}`}
      onPress={() => setActiveTab(value)}
    >
      <Text
        className={`text-sm font-medium ${
          isActive
            ? 'text-primary-500'
            : 'text-typography-500 dark:text-typography-400'
        }`}
      >
        {children}
      </Text>
    </Pressable>
  );
}

// TabsContent component
interface TabsContentProps {
  value: string;
  children: React.ReactNode;
  className?: string;
}

export function TabsContent({
  value,
  children,
  className = '',
}: TabsContentProps) {
  const { activeTab } = useTabsContext();
  
  if (activeTab !== value) {
    return null;
  }

  return (
    <Box className={`py-4 ${className}`}>
      {children}
    </Box>
  );
}
