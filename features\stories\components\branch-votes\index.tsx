import React from 'react';
import { useTranslation } from 'react-i18next';
import { ThumbsUp, ThumbsDown } from 'lucide-react-native';
import { BranchVote, BranchVoteStats } from '@/api/stories';
import { useAuthStore } from '@/lib/store/auth-store';

// Import gluestack-ui components
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/m3e-button';
import { ButtonIcon } from '@/components/ui/m3e-button';
import { ButtonText } from '@/components/ui/m3e-button';
import { Spinner } from '@/components/ui/spinner';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';

interface BranchVotesProps {
  voteStats: BranchVoteStats | null;
  userVote: BranchVote | null;
  isLoading: boolean;
  isVoting: boolean;
  onVote: (voteType: 'up' | 'down') => Promise<boolean>;
}

export default function BranchVotes({
  voteStats,
  userVote,
  isLoading,
  isVoting,
  onVote,
}: BranchVotesProps) {
  const { t } = useTranslation();
  const { user } = useAuthStore();

  // 处理投票
  const handleVote = async (voteType: 'up' | 'down') => {
    if (isVoting) return;
    await onVote(voteType);
  };

  // 获取投票统计
  const getVoteStats = () => {
    if (!voteStats) {
      return {
        upvotes: 0,
        downvotes: 0,
        total: 0,
      };
    }
    return voteStats;
  };

  // 检查用户是否已投票
  const hasUserVoted = (voteType: 'up' | 'down') => {
    return userVote?.vote_type === voteType;
  };

  // 渲染投票按钮
  const renderVoteButtons = () => {
    const stats = getVoteStats();

    return (
      <HStack space="md" justifyContent="center" className="mb-4">
        <Button
          variant={hasUserVoted('up') ? 'solid' : 'outline'}
          action={hasUserVoted('up') ? 'positive' : 'secondary'}
          size="sm"
          className="flex-row items-center px-4 py-2 rounded-md"
          onPress={() => handleVote('up')}
          isDisabled={!user || isVoting}
          opacity={!user || isVoting ? 0.5 : 1}
        >
          <ButtonIcon as={ThumbsUp} size="sm" />
          <ButtonText className="ml-1">{stats.upvotes}</ButtonText>
        </Button>

        <Button
          variant={hasUserVoted('down') ? 'solid' : 'outline'}
          action={hasUserVoted('down') ? 'negative' : 'secondary'}
          size="sm"
          className="flex-row items-center px-4 py-2 rounded-md"
          onPress={() => handleVote('down')}
          isDisabled={!user || isVoting}
          opacity={!user || isVoting ? 0.5 : 1}
        >
          <ButtonIcon as={ThumbsDown} size="sm" />
          <ButtonText className="ml-1">{stats.downvotes}</ButtonText>
        </Button>
      </HStack>
    );
  };

  // 渲染投票统计
  const renderVoteStats = () => {
    const stats = getVoteStats();
    const totalVotes = stats.upvotes + stats.downvotes;

    // 计算投票比例
    const upvotePercentage =
      totalVotes > 0 ? (stats.upvotes / totalVotes) * 100 : 0;
    const downvotePercentage =
      totalVotes > 0 ? (stats.downvotes / totalVotes) * 100 : 0;

    return (
      <Box className="mt-2">
        <Box className="h-2 bg-surface-100 dark:bg-surface-800 rounded-full overflow-hidden flex-row">
          <Box
            className="h-full bg-success-500"
            style={{ width: `${upvotePercentage}%` }}
          />
          <Box
            className="h-full bg-error-500"
            style={{ width: `${downvotePercentage}%` }}
          />
        </Box>

        <Text className="text-sm font-medium text-center mt-2">
          {t('storyDetail.voteTotal', 'Total Score')}: {stats.total}
        </Text>
      </Box>
    );
  };

  if (isLoading) {
    return (
      <Box className="bg-background-50 dark:bg-background-900 rounded-md border border-outline-200 dark:border-outline-700 overflow-hidden my-4">
        <Box className="p-6 items-center justify-center">
          <Spinner size="small" color="$primary500" />
          <Text className="text-sm font-medium text-secondary-500 dark:text-secondary-400 mt-2">
            {t('storyDetail.loadingVotes', 'Loading votes...')}
          </Text>
        </Box>
      </Box>
    );
  }

  return (
    <Box className="bg-background-50 dark:bg-background-900 rounded-md border border-outline-200 dark:border-outline-700 overflow-hidden my-4">
      <Box className="p-4 border-b border-outline-200 dark:border-outline-700 bg-surface-50 dark:bg-surface-900">
        <Text className="text-base font-bold text-typography-900 dark:text-typography-100">
          {t('storyDetail.branchRating', 'Branch Rating')}
        </Text>
      </Box>

      <Box className="p-4">
        {renderVoteButtons()}
        {renderVoteStats()}

        {!user && (
          <Text className="text-sm font-medium text-secondary-500 dark:text-secondary-400 text-center mt-4">
            {t('storyDetail.loginToVote', 'Please log in to vote')}
          </Text>
        )}
      </Box>
    </Box>
  );
}
