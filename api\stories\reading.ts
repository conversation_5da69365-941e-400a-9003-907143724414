import { supabase } from '@/utils/supabase';
import { PostgrestError } from '@supabase/supabase-js';
import { Story, StoryWithSegments, GetStoriesOptions } from './types';
import { Profile } from '../profiles';

/**
 * 检查表中是否存在指定列
 * @param tableName 表名
 * @param columnName 列名
 * @returns 是否存在
 */
async function hasColumn(
  tableName: string,
  columnName: string
): Promise<boolean> {
  try {
    // 使用 information_schema 查询列是否存在
    const { data, error } = await supabase.rpc('column_exists', {
      table_name: tableName,
      column_name: columnName,
    });

    if (error) {
      console.error(
        `Error checking if column ${columnName} exists in ${tableName}:`,
        error
      );
      // 默认假设列存在，避免查询失败导致功能不可用
      return true;
    }

    return !!data;
  } catch (error) {
    console.error(`Unexpected error checking column existence:`, error);
    // 默认假设列存在，避免查询失败导致功能不可用
    return true;
  }
}

/**
 * Gets a list of stories with basic author info and first segment preview.
 */
export async function getStories(
  options: GetStoriesOptions = {}
): Promise<{ data: Story[] | null; error: PostgrestError | null }> {
  const {
    limit = 10,
    offset = 0,
    authorId,
    filter = 'latest',
    searchTerm,
    tags,
    sortBy,
    sortOrder = 'desc',
  } = options;
  const {
    data: { user },
  } = await supabase.auth.getUser();

  // RPC 'get_stories_with_details' is preferred for fetching stories with like counts and user like status.
  // This client-side version is a fallback or for simpler cases.
  // If 'get_stories_with_details' RPC exists and works, it should be prioritized.
  // For now, building the query client-side:

  let query = supabase.from('stories').select(`
      id, title, author_id, cover_image_url, created_at, updated_at, status, visibility, tags,
      profiles!stories_author_id_fkey ( id, username, avatar_url ),
      story_segments!inner ( content, created_at, order_in_branch )
    `);
  // Using !inner on story_segments to ensure they exist, effectively filtering stories without authors or initial segments.
  // This is a strong assumption for MVP; might need adjustment if stories can exist without an initial segment.

  // Base filters for public, published stories unless specified otherwise
  if (filter !== 'my_drafts' && filter !== 'my_published') {
    query = query.eq('status', 'published').eq('visibility', 'public');
  }

  if (authorId) {
    query = query.eq('author_id', authorId);
  }

  if (filter === 'my_drafts' && user) {
    query = query.eq('author_id', user.id).eq('status', 'draft');
  } else if (filter === 'my_published' && user) {
    query = query.eq('author_id', user.id).eq('status', 'published');
  } else if (filter === 'following' && user) {
    // 获取当前用户关注的用户列表
    const { data: followingData } = await supabase
      .from('user_follows')
      .select('following_id')
      .eq('follower_id', user.id);

    if (followingData && followingData.length > 0) {
      // 提取关注用户的ID列表
      const followingIds = followingData.map((item) => item.following_id);
      // 筛选这些用户创建的故事
      query = query.in('author_id', followingIds);
    } else {
      // 如果用户没有关注任何人，返回空结果
      // 这里使用一个不可能匹配的条件，确保返回空结果
      query = query.eq('id', '00000000-0000-0000-0000-000000000000');
    }
  }

  if (searchTerm) {
    query = query.ilike('title', `%${searchTerm}%`);
  }

  if (tags && tags.length > 0) {
    query = query.overlaps('tags', tags); // Assumes tags is array in DB
  }

  // 排序逻辑
  if (sortBy) {
    // 如果提供了明确的排序字段，使用它
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });
  } else if (filter === 'popular') {
    // 'popular' 排序基于点赞数和浏览量
    // 如果数据库中有 likes_count 字段，优先使用它
    if (await hasColumn('stories', 'likes_count')) {
      query = query.order('likes_count', { ascending: false });
    } else {
      // 否则按更新时间排序
      query = query.order('updated_at', { ascending: false });
    }
  } else if (filter === 'recommended' && user) {
    // 推荐逻辑：基于用户喜欢的故事的标签，推荐相似标签的故事
    // 获取用户喜欢的故事
    const { data: likedStories } = await supabase
      .from('story_likes')
      .select('story_id')
      .eq('user_id', user.id);

    if (likedStories && likedStories.length > 0) {
      // 获取这些故事的标签
      const likedStoryIds = likedStories.map((item) => item.story_id);
      const { data: storiesWithTags } = await supabase
        .from('stories')
        .select('tags')
        .in('id', likedStoryIds)
        .not('tags', 'is', null);

      if (storiesWithTags && storiesWithTags.length > 0) {
        // 提取所有标签并去重
        const allTags = storiesWithTags
          .flatMap((story) => story.tags || [])
          .filter(Boolean);

        if (allTags.length > 0) {
          // 使用这些标签筛选故事，但排除用户已经喜欢的故事
          query = query
            .overlaps('tags', allTags)
            .not('id', 'in', likedStoryIds);
        }
      }
    }

    // 默认排序
    query = query.order('updated_at', { ascending: false });
  } else if (
    filter === 'latest' ||
    filter === 'my_drafts' ||
    filter === 'my_published' ||
    filter === 'following'
  ) {
    // 默认按更新时间排序
    query = query.order('updated_at', { ascending: false });
  } else {
    // 其他情况默认按更新时间排序
    query = query.order('updated_at', { ascending: false });
  }

  query = query.limit(limit).range(offset, offset + limit - 1);

  // Filter story_segments to only get the first one (order_in_branch = 0 or earliest created_at)
  // This is tricky with Supabase select strings for nested filtering.
  // An RPC or view would be better here.
  // For now, we fetch all segments and process client-side, which is inefficient for lists.
  // A better select for story_segments might be: story_segments(content, created_at, order_in_branch).eq('order_in_branch',0).limit(1)
  // However, the .eq and .limit on nested selects has limitations.
  // Let's try to order and limit on the nested select directly.
  // query = query.select(`..., story_segments!inner ( content, created_at, order_in_branch ).order(order_in_branch, {ascending: true}).limit(1)`)

  const { data: rawData, error } = await query;

  if (error) {
    console.error('Error fetching stories:', error);
    return { data: null, error };
  }

  // Process data to extract first segment preview and ensure correct typing
  const processedData =
    rawData?.map((story) => {
      const typedStory = story as any; // Cast to any to access nested parts before typing

      let firstSegmentContent: string | null = null;
      if (
        typedStory.story_segments &&
        Array.isArray(typedStory.story_segments) &&
        typedStory.story_segments.length > 0
      ) {
        // Sort by order_in_branch then created_at to be sure
        const sortedSegments = typedStory.story_segments.sort(
          (a: any, b: any) => {
            if (a.order_in_branch !== b.order_in_branch) {
              return a.order_in_branch - b.order_in_branch;
            }
            return (
              new Date(a.created_at).getTime() -
              new Date(b.created_at).getTime()
            );
          }
        );
        const firstSegment = sortedSegments[0];
        firstSegmentContent =
          firstSegment?.content.substring(0, 150) +
            (firstSegment?.content.length > 150 ? '...' : '') || null;
      }

      return {
        id: typedStory.id,
        author_id: typedStory.author_id,
        title: typedStory.title,
        status: typedStory.status,
        visibility: typedStory.visibility,
        tags: typedStory.tags,
        cover_image_url: typedStory.cover_image_url,
        created_at: typedStory.created_at,
        updated_at: typedStory.updated_at,
        profiles: typedStory.profiles as Pick<
          Profile,
          'username' | 'avatar_url' | 'id'
        > | null,
        first_segment_content: firstSegmentContent,
        // like_count and is_liked_by_user would come from an RPC or more complex query
      } as Story;
    }) || null;

  return { data: processedData, error: null };
}

/**
 * Gets a single story by ID, including its segments and author details with pagination.
 * Segments are ordered chronologically.
 */
export async function getStoryWithSegmentsById(
  storyId: string,
  page: number = 0,
  pageSize: number = 20
): Promise<{
  data: StoryWithSegments | null;
  error: PostgrestError | null;
  hasMore: boolean;
}> {
  console.log(
    `Fetching story with ID: ${storyId}, page: ${page}, pageSize: ${pageSize}`
  );

  try {
    // 首先获取故事基本信息（不包含段落）
    const { data: storyData, error: storyError } = await supabase
      .from('stories')
      .select(
        `
        id, title, author_id, cover_image_url, created_at, updated_at, status, visibility, tags,
        profiles!stories_author_id_fkey ( id, username, avatar_url )
      `
      )
      .eq('id', storyId)
      .single();

    if (storyError) {
      console.error(`Error fetching story ${storyId}:`, storyError);
      return { data: null, error: storyError, hasMore: false };
    }

    // 获取段落总数，用于判断是否有更多段落
    const { count: totalSegments, error: countError } = await supabase
      .from('story_segments')
      .select('id', { count: 'exact', head: true })
      .eq('story_id', storyId);

    if (countError) {
      console.error(
        `Error counting segments for story ${storyId}:`,
        countError
      );
      // 继续执行，不中断流程
    }

    // 分页获取段落
    const { data: segmentsData, error: segmentsError } = await supabase
      .from('story_segments')
      .select(
        `
        id, author_id, content_type, content, parent_segment_id, order_in_branch, is_ai_generated, created_at, updated_at, story_id,
        profiles!story_segments_author_id_fkey ( id, username, avatar_url )
      `
      )
      .eq('story_id', storyId)
      .order('created_at', { ascending: true })
      .range(page * pageSize, (page + 1) * pageSize - 1);

    if (segmentsError) {
      console.error(
        `Error fetching segments for story ${storyId}:`,
        segmentsError
      );
      return { data: null, error: segmentsError, hasMore: false };
    }

    // 计算每个段落的子段落数量
    const { data: childrenCountData, error: childrenCountError } =
      await supabase.rpc('get_segment_children_count', {
        story_id_param: storyId,
      });

    if (childrenCountError) {
      console.error(
        `Error fetching children count for story ${storyId}:`,
        childrenCountError
      );
      // 继续执行，不中断流程
    }

    // 创建段落ID到子段落数量的映射
    const segmentChildrenCount = new Map<string, number>();
    if (childrenCountData) {
      childrenCountData.forEach((item: any) => {
        segmentChildrenCount.set(
          item.segment_id,
          parseInt(item.children_count)
        );
      });
    } else {
      // 如果RPC调用失败，使用传统方法计算子段落数量
      segmentsData.forEach((segment: any) => {
        if (segment.parent_segment_id) {
          const count =
            segmentChildrenCount.get(segment.parent_segment_id) || 0;
          segmentChildrenCount.set(segment.parent_segment_id, count + 1);
        }
      });
    }

    // 添加 children_count 属性
    const typedSegments = segmentsData as any[];
    typedSegments.forEach((segment) => {
      segment.children_count = segmentChildrenCount.get(segment.id) || 0;
      console.log(
        `Set children_count for segment ${segment.id} to ${segment.children_count}`
      );
    });

    // 排序段落
    typedSegments.sort((a, b) => {
      // Prioritize segments with no parent (root segments) if mixing
      if (a.parent_segment_id === null && b.parent_segment_id !== null)
        return -1;
      if (a.parent_segment_id !== null && b.parent_segment_id === null)
        return 1;

      // Then sort by order_in_branch if available and different
      if (a.order_in_branch !== b.order_in_branch) {
        return a.order_in_branch - b.order_in_branch;
      }
      // Fallback to creation time
      return (
        new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      );
    });

    // 将段落添加到故事对象中
    const result: StoryWithSegments = {
      id: storyData.id,
      title: storyData.title,
      author_id: storyData.author_id,
      cover_image_url: storyData.cover_image_url,
      created_at: storyData.created_at,
      updated_at: storyData.updated_at,
      status: storyData.status,
      visibility: storyData.visibility,
      tags: storyData.tags,
      profiles: storyData.profiles
        ? {
            id: storyData.profiles[0]?.id,
            username: storyData.profiles[0]?.username,
            avatar_url: storyData.profiles[0]?.avatar_url,
          }
        : null,
      story_segments: typedSegments,
    };

    // 判断是否有更多段落
    const hasMore = totalSegments
      ? (page + 1) * pageSize < totalSegments
      : false;

    return { data: result, error: null, hasMore };
  } catch (error) {
    console.error(`Unexpected error fetching story ${storyId}:`, error);
    return { data: null, error: error as PostgrestError, hasMore: false };
  }
}

/**
 * 创建一个存储过程，用于计算每个段落的子段落数量
 * 这个函数需要在 Supabase 中执行一次，之后就可以使用 RPC 调用
 */
export async function createGetSegmentChildrenCountFunction() {
  const { error } = await supabase.rpc(
    'create_get_segment_children_count_function'
  );

  if (error) {
    console.error('Error creating get_segment_children_count function:', error);
    return false;
  }

  return true;
}
