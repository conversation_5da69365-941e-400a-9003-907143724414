import React from 'react';
import { Box } from '@/components/ui/box';
import { Crown } from 'lucide-react-native';

interface PremiumBadgeProps {
  visible: boolean;
}

export function PremiumBadge({ visible }: PremiumBadgeProps) {
  if (!visible) return null;

  return (
    <Box className="absolute top-2 right-2 bg-yellow-400 p-1 rounded-full">
      <Crown size={10} color="#000" />
    </Box>
  );
}
