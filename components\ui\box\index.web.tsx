import React from 'react';
import { boxStyle } from './styles.tsx';
import { processWebStyle, useWebLayout } from '@/utils/web-style-helper';

import type { VariantProps } from '@gluestack-ui/nativewind-utils';

type IBoxProps = React.ComponentPropsWithoutRef<'div'> &
  VariantProps<typeof boxStyle> & {
    className?: string;
    style?: React.CSSProperties | React.CSSProperties[];
    onLayout?: (event: {
      nativeEvent: {
        layout: { width: number; height: number; x: number; y: number };
      };
    }) => void;
  };

const Box = React.forwardRef<HTMLDivElement, IBoxProps>(function Box(
  { className, style, onLayout, ...props },
  ref
) {
  // 创建内部 ref，如果没有提供外部 ref
  const innerRef = React.useRef<HTMLDivElement>(null);
  const actualRef = (ref as React.RefObject<HTMLDivElement>) || innerRef;

  // 处理样式数组
  const processedStyle = style ? processWebStyle(style) : undefined;

  // 处理 onLayout 事件
  useWebLayout(actualRef, onLayout);

  // 过滤掉 React Native 特有的属性
  const webProps = { ...props };
  delete (webProps as any).onLayout;

  // Convert React Native props to lowercase for web
  if ((props as any).accessible !== undefined) {
    (webProps as any).accessible = undefined;
    (webProps as any).accessible = (props as any).accessible;
  }

  if ((props as any).accessibilityElementsHidden !== undefined) {
    (webProps as any).accessibilityelementshidden = (
      props as any
    ).accessibilityElementsHidden;
    delete (webProps as any).accessibilityElementsHidden;
  }

  if ((props as any).dataSet !== undefined) {
    (webProps as any).dataset = (props as any).dataSet;
    delete (webProps as any).dataSet;
  }

  return (
    <div
      ref={actualRef}
      className={boxStyle({ class: className })}
      style={processedStyle}
      {...webProps}
    />
  );
});

Box.displayName = 'Box';
export { Box };
