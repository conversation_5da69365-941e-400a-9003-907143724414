import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSettingsStore, ThemeMode } from '@/lib/store/settings-store';
import { Box } from '@/components/ui/box';
import { Pressable } from '@/components/ui/pressable/index';
import { Text } from '@/components/ui/text';
import { useColorScheme } from 'nativewind';

export function ThemeOptionsGroup() {
  const { t } = useTranslation('settings');
  const themeMode = useSettingsStore((state) => state.themeMode);
  const setThemeMode = useSettingsStore((state) => state.setThemeMode);
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Define theme options
  const themeOptions: { value: ThemeMode; label: string }[] = [
    { value: 'light', label: t('lightMode') },
    { value: 'dark', label: t('darkMode') },
    { value: 'system', label: t('systemMode') },
  ];

  return (
    <Box className="w-full flex-row">
      {themeOptions.map((option) => (
        <Pressable
          key={option.value}
          onPress={() => setThemeMode(option.value)}
          className={`flex-1 items-center justify-center py-4 ${
            themeMode === option.value
              ? 'bg-primary-500'
              : isDark
              ? 'bg-background-800'
              : 'bg-background-200'
          }`}
        >
          <Text
            className={`font-medium ${
              themeMode === option.value
                ? 'text-white' // 选中项始终使用白色文本
                : isDark
                ? 'text-typography-50'
                : 'text-typography-900'
            }`}
          >
            {option.label}
          </Text>
        </Pressable>
      ))}
    </Box>
  );
}
