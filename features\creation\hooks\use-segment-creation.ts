import { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';
import { supabase } from '@/utils/supabase';
import { useAuthStore } from '@/lib/store/auth-store';

interface UseSegmentCreationProps {
  storyId?: string;
  parentSegmentId?: string;
}

export function useSegmentCreation({
  storyId,
  parentSegmentId,
}: UseSegmentCreationProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const { user } = useAuthStore();

  const [content, setContent] = useState('');
  const [title, setTitle] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isAIGenerated, setIsAIGenerated] = useState(false);

  const handleSubmit = useCallback(async () => {
    if (!storyId || !parentSegmentId || !user?.id) {
      setError(t('createSegment.missingParams', 'Missing required parameters'));
      return;
    }

    if (!content.trim()) {
      setError(t('createSegment.contentRequired', 'Content is required'));
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Format content with title if provided
      const formattedContent = title.trim()
        ? `[${title.trim()}] ${content.trim()}`
        : content.trim();

      // Insert new segment
      const { data, error: insertError } = await supabase
        .from('story_segments')
        .insert({
          story_id: storyId,
          author_id: user.id,
          content: formattedContent,
          parent_segment_id: parentSegmentId,
          is_ai_generated: isAIGenerated,
        })
        .select('id')
        .single();

      if (insertError) {
        throw new Error(insertError.message);
      }

      // Navigate back to story detail
      router.replace(`/stories/${storyId}`);
    } catch (err) {
      console.error('Error creating segment:', err);
      setError(
        err instanceof Error
          ? err.message
          : t('createSegment.unknownError', 'An unknown error occurred')
      );
    } finally {
      setIsSubmitting(false);
    }
  }, [
    storyId,
    parentSegmentId,
    user?.id,
    content,
    title,
    router,
    t,
    isAIGenerated,
  ]);

  const handleCancel = useCallback(() => {
    router.back();
  }, [router]);

  return {
    content,
    setContent,
    title,
    setTitle,
    isSubmitting,
    error,
    isAIGenerated,
    setIsAIGenerated,
    handleSubmit,
    handleCancel,
  };
}
