import React from 'react';
import { useTranslation } from 'react-i18next';
import { HStack } from '@/components/ui/hstack/index';
import { VStack } from '@/components/ui/vstack/index';
import { Text } from '@/components/ui/text';
import { Box } from '@/components/ui/box';

interface ProfileStatsProps {
  posts: number;
  followers: number;
  following: number;
}

export function ProfileStats({
  posts,
  followers,
  following,
}: ProfileStatsProps) {
  const { t } = useTranslation();

  const StatItem = ({ value, label }: { value: number; label: string }) => (
    <VStack className="flex-1 items-center justify-center">
      <Text className="text-xl font-bold text-typography-50">{value}</Text>
      <Text className="text-sm text-typography-400 mt-1">{label}</Text>
    </VStack>
  );

  return (
    <Box className="bg-background-950">
      <HStack className="justify-around py-5 border-t border-b border-outline-700">
        <StatItem value={posts} label={t('profile.stories', '故事')} />
        <StatItem value={followers} label={t('profile.followers', '粉丝')} />
        <StatItem value={following} label={t('profile.following', '关注')} />
      </HStack>
    </Box>
  );
}
