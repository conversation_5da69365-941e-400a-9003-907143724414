import { create } from 'zustand';
import { User, Session } from '@supabase/supabase-js';
import { getCurrentUser } from '@/api/auth';
import AsyncStorage from '@react-native-async-storage/async-storage'; // Assuming async storage is used for persistence
import { createJSONStorage, persist, StateStorage } from 'zustand/middleware';

interface AuthState {
  user: User | null;
  session: Session | null; // Correctly typed with Session
  isLoading: boolean;
  isInitialized: boolean; // Flag to indicate if the initial check is done
  _hasHydrated: boolean; // Add _hasHydrated state
  signIn: (session: Session, user: User) => void; // Correctly typed with Session
  signOut: () => void;
  initializeAuth: () => Promise<void>;
  setHasHydrated: (status: boolean) => void; // Action to set hydration status
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      session: null,
      isLoading: true,
      isInitialized: false,
      _hasHydrated: false, // Initialize _hasHydrated to false
      signIn: (session, user) => set({ session, user, isLoading: false }),
      signOut: () => set({ user: null, session: null, isLoading: false }),
      setHasHydrated: (status) => set({ _hasHydrated: status }), // Implement setHasHydrated
      initializeAuth: async () => {
        // Only proceed if store has hydrated to prevent race conditions
        if (!get()._hasHydrated) {
          // You might want to queue this or handle it differently if hydration is slow
          // For now, just wait for a short period or rely on re-render triggering this again
          // A more robust solution might involve a listener for hydration completion.
          return;
        }
        set({ isLoading: true, isInitialized: false });
        try {
          const { data, error } = await getCurrentUser(); // getCurrentUser now returns { data: { user, session } | null, error }
          if (error) {
            console.error('Error initializing auth state:', error);
            set({
              user: null,
              session: null,
              isLoading: false,
              isInitialized: true,
            });
          } else if (data && data.user && data.session) {
            // Check if data and data.user/data.session exist
            set({
              user: data.user,
              session: data.session,
              isLoading: false,
              isInitialized: true,
            });
          } else {
            // No authenticated user or session found
            set({
              user: null,
              session: null,
              isLoading: false,
              isInitialized: true,
            });
          }
        } catch (err) {
          // Catch block for any other errors
          console.error('Failed to initialize auth state:', err);
          set({
            user: null,
            session: null,
            isLoading: false,
            isInitialized: true,
          });
        }
      },
    }),
    {
      name: 'auth-storage', // unique name
      storage: createJSONStorage(() => AsyncStorage), // Use AsyncStorage for React Native
      // We don't need to store the whole state, just a flag or perhaps the user ID if needed
      // Zustand's persist middleware handles rehydrating the state, but Supabase also handles session persistence.
      // We primarily use this store to reactively update UI based on auth state.
      // The 'initializeAuth' function will fetch the *actual* state from Supabase.
      // So, we might not need to persist user/session here, maybe just initialization flag.
      // However, for simplicity and quick UI updates on app start, storing user/session might be okay.
      // Let's persist user and session for now, but be mindful of potential sync issues with Supabase's own persistence.
      partialize: (state) => ({
        user: state.user,
        session: state.session, // May contain sensitive info, reconsider if needed
        isInitialized: state.isInitialized, // Persist initialization status
      }),
      onRehydrateStorage: (state) => {
        // This callback is called when the storage is rehydrated.
        // We can set _hasHydrated to true here.
        // console.log('Auth store has been rehydrated');
        // Directly calling setHasHydrated might not work as `set` from `persist` is different.
        // Instead, we use the `state` argument if it has the setter, or call the action.
        // A common pattern is to call an action on the store.
        // useAuthStore.getState().setHasHydrated(true); // This is how you call an action from outside React components
        // However, to avoid issues with initial render, it is safer to do it inside the persist config.
        // The `state` argument here represents the rehydrated state but doesn't have access to `set` directly for _hasHydrated.
        // Let's modify the initial state and an action.
        return (currentState, error) => {
          if (error) {
            console.error(
              'An error occurred during auth store rehydration:',
              error
            );
          } else {
            useAuthStore.setState({ _hasHydrated: true });
          }
        };
      },
    }
  )
);
