import { supabase } from '@/utils/supabase';
import { PostgrestError } from '@supabase/supabase-js';
import { Profile } from '../profiles';
import { createNotification } from '../notifications';

export interface Comment {
  id: string;
  story_id: string;
  user_id: string;
  content: string;
  parent_comment_id?: string | null;
  created_at: string;
  updated_at: string;
  // 嵌入的关联数据
  profiles?: Pick<Profile, 'id' | 'username' | 'avatar_url'> | null;
  // 子评论数量
  reply_count?: number;
}

export interface GetCommentsOptions {
  limit?: number;
  offset?: number;
  parentCommentId?: string | null;
}

/**
 * 获取故事的评论
 * @param storyId 故事ID
 * @param options 选项
 * @returns 评论列表
 */
export async function getStoryComments(
  storyId: string,
  options: GetCommentsOptions = {}
): Promise<{ data: Comment[] | null; error: PostgrestError | null }> {
  const { limit = 20, offset = 0, parentCommentId = null } = options;
  
  let query = supabase
    .from('story_comments')
    .select(`
      id, story_id, user_id, content, parent_comment_id, created_at, updated_at,
      profiles!story_comments_user_id_fkey ( id, username, avatar_url )
    `)
    .eq('story_id', storyId)
    .is('parent_comment_id', parentCommentId);
  
  // 排序和分页
  query = query
    .order('created_at', { ascending: true })
    .range(offset, offset + limit - 1);
  
  const { data, error } = await query;
  
  if (error) {
    console.error('Error getting story comments:', error);
    return { data: null, error };
  }
  
  // 获取每个评论的回复数量
  if (data && data.length > 0 && parentCommentId === null) {
    const commentIds = data.map(comment => comment.id);
    
    const { data: replyCounts, error: replyCountError } = await supabase
      .from('story_comments')
      .select('parent_comment_id, count(*)')
      .in('parent_comment_id', commentIds)
      .group('parent_comment_id');
    
    if (!replyCountError && replyCounts) {
      // 创建一个映射，将父评论ID映射到回复数量
      const replyCountMap = replyCounts.reduce((map, item) => {
        map[item.parent_comment_id] = parseInt(item.count);
        return map;
      }, {} as Record<string, number>);
      
      // 将回复数量添加到评论数据中
      data.forEach(comment => {
        comment.reply_count = replyCountMap[comment.id] || 0;
      });
    }
  }
  
  return { data: data as Comment[], error: null };
}

/**
 * 添加评论
 * @param storyId 故事ID
 * @param content 评论内容
 * @param parentCommentId 父评论ID（可选）
 * @returns 新创建的评论
 */
export async function addStoryComment(
  storyId: string,
  content: string,
  parentCommentId?: string
): Promise<{ data: Comment | null; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  
  if (!user) {
    return {
      data: null,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };
  }
  
  // 获取故事信息，用于通知
  const { data: storyData, error: storyError } = await supabase
    .from('stories')
    .select('title, author_id')
    .eq('id', storyId)
    .single();
  
  if (storyError) {
    console.error('Error getting story info:', storyError);
    return { data: null, error: storyError };
  }
  
  // 创建评论
  const { data, error } = await supabase
    .from('story_comments')
    .insert({
      story_id: storyId,
      user_id: user.id,
      content,
      parent_comment_id: parentCommentId,
    })
    .select(`
      id, story_id, user_id, content, parent_comment_id, created_at, updated_at,
      profiles!story_comments_user_id_fkey ( id, username, avatar_url )
    `)
    .single();
  
  if (error) {
    console.error('Error adding story comment:', error);
    return { data: null, error };
  }
  
  // 如果是回复评论，获取父评论的作者ID
  let notifyUserId = storyData.author_id;
  let notificationType: 'comment' | 'reply' = 'comment';
  
  if (parentCommentId) {
    const { data: parentComment, error: parentError } = await supabase
      .from('story_comments')
      .select('user_id')
      .eq('id', parentCommentId)
      .single();
    
    if (!parentError && parentComment) {
      notifyUserId = parentComment.user_id;
      notificationType = 'reply';
    }
  }
  
  // 如果评论者不是被通知者，创建通知
  if (user.id !== notifyUserId) {
    try {
      await createNotification({
        user_id: notifyUserId,
        type: notificationType,
        title: notificationType === 'comment' 
          ? '新评论' 
          : '新回复',
        body: notificationType === 'comment'
          ? `有人评论了你的故事 "${storyData.title}"`
          : `有人回复了你的评论`,
        data: {
          story_id: storyId,
          comment_id: data.id,
        },
        is_read: false,
        actor_id: user.id,
        story_id: storyId,
      });
    } catch (notifyError) {
      console.error('Error creating notification:', notifyError);
      // 不中断流程，继续返回评论数据
    }
  }
  
  return { data: data as Comment, error: null };
}

/**
 * 更新评论
 * @param commentId 评论ID
 * @param content 新的评论内容
 * @returns 更新后的评论
 */
export async function updateStoryComment(
  commentId: string,
  content: string
): Promise<{ data: Comment | null; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  
  if (!user) {
    return {
      data: null,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };
  }
  
  // 检查评论是否属于当前用户
  const { data: existingComment, error: checkError } = await supabase
    .from('story_comments')
    .select('user_id')
    .eq('id', commentId)
    .single();
  
  if (checkError) {
    console.error('Error checking comment ownership:', checkError);
    return { data: null, error: checkError };
  }
  
  if (existingComment.user_id !== user.id) {
    return {
      data: null,
      error: {
        message: 'You can only update your own comments',
        details: '',
        hint: '',
        code: '403',
      } as PostgrestError,
    };
  }
  
  // 更新评论
  const { data, error } = await supabase
    .from('story_comments')
    .update({ content })
    .eq('id', commentId)
    .select(`
      id, story_id, user_id, content, parent_comment_id, created_at, updated_at,
      profiles!story_comments_user_id_fkey ( id, username, avatar_url )
    `)
    .single();
  
  if (error) {
    console.error('Error updating story comment:', error);
    return { data: null, error };
  }
  
  return { data: data as Comment, error: null };
}

/**
 * 删除评论
 * @param commentId 评论ID
 * @returns 成功状态
 */
export async function deleteStoryComment(
  commentId: string
): Promise<{ success: boolean; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  
  if (!user) {
    return {
      success: false,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };
  }
  
  // 检查评论是否属于当前用户
  const { data: existingComment, error: checkError } = await supabase
    .from('story_comments')
    .select('user_id')
    .eq('id', commentId)
    .single();
  
  if (checkError) {
    console.error('Error checking comment ownership:', checkError);
    return { success: false, error: checkError };
  }
  
  if (existingComment.user_id !== user.id) {
    return {
      success: false,
      error: {
        message: 'You can only delete your own comments',
        details: '',
        hint: '',
        code: '403',
      } as PostgrestError,
    };
  }
  
  // 删除评论
  const { error } = await supabase
    .from('story_comments')
    .delete()
    .eq('id', commentId);
  
  if (error) {
    console.error('Error deleting story comment:', error);
    return { success: false, error };
  }
  
  return { success: true, error: null };
}

/**
 * 获取评论数量
 * @param storyId 故事ID
 * @returns 评论数量
 */
export async function getStoryCommentCount(
  storyId: string
): Promise<{ count: number; error: PostgrestError | null }> {
  const { count, error } = await supabase
    .from('story_comments')
    .select('id', { count: 'exact', head: true })
    .eq('story_id', storyId);
  
  if (error) {
    console.error('Error getting story comment count:', error);
    return { count: 0, error };
  }
  
  return { count: count || 0, error: null };
}