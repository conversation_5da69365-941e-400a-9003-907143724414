import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'expo-router';

// Import custom hook
import { useProfileData } from '../hooks/use-profile-data';

// Import components
import { ProfileScreenLoading } from '../components/profile-screen-loading';
import { ProfileScreenAuth } from '../components/profile-screen-auth';
import { ProfileScreenError } from '../components/profile-screen-error';
import { ProfileScreenContent } from '../components/profile-screen-content';
import { Text } from '@/components/ui/text';
import { Spinner } from '@/components/ui/spinner';

export default function ProfileScreen() {
  const { t } = useTranslation();
  const router = useRouter();

  const {
    profile,
    profileError,
    userStories,
    storiesLoading,
    storiesError,
    isLoading,
    authUser,
    authStoreInitialized,
  } = useProfileData();

  const handleEditProfile = () => {
    router.push('/(profile)/edit');
  };

  const handleShareProfile = () => {
    console.log('Share Profile pressed');
  };

  const handleStoryPress = (storyId: string) => {
    console.log('Story pressed:', storyId);
    router.push(`/stories/${storyId}`);
  };

  const navigateToLogin = () => {
    router.push('/(auth)/login');
  };

  const navigateToRegister = () => {
    router.push('/(auth)/register');
  };

  // Show loading or uninitialized state if necessary
  if (!authStoreInitialized || isLoading) {
    return <ProfileScreenLoading />;
  }

  // If user is not authenticated after initialization
  if (!authUser) {
    return (
      <ProfileScreenAuth
        onLogin={navigateToLogin}
        onRegister={navigateToRegister}
      />
    );
  }

  // If authenticated but profile fetch failed
  if (!profile && profileError) {
    return <ProfileScreenError error={profileError} />;
  }

  // If authenticated but profile is somehow null and not loading (edge case)
  if (!profile) {
    return (
      <View className="flex-1 bg-background-100 justify-center items-center">
        <Spinner size="large" color="#0891b2" />
        <Text className="mt-2">{t('loading')}</Text>
      </View>
    );
  }

  // User is authenticated and profile is loaded
  return (
    <ProfileScreenContent
      profile={profile}
      userStories={userStories}
      storiesLoading={storiesLoading}
      storiesError={storiesError}
      onEditProfile={handleEditProfile}
      onShareProfile={handleShareProfile}
      onStoryPress={handleStoryPress}
    />
  );
}
