import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Modal, ScrollView } from 'react-native';


// Date Picker 的属性接口
export interface M3EDatePickerProps {
  /** 当前日期值 */
  value?: Date;
  /** 日期变化事件 */
  onDateChange?: (date: Date) => void;
  /** 最小日期 */
  minimumDate?: Date;
  /** 最大日期 */
  maximumDate?: Date;
  /** 是否禁用 */
  disabled?: boolean;
  /** 标签文本 */
  label?: string;
  /** 占位符文本 */
  placeholder?: string;
  /** 日期格式 */
  dateFormat?: 'YYYY-MM-DD' | 'MM/DD/YYYY' | 'DD/MM/YYYY';
  /** 自定义样式类名 */
  className?: string;
}

// 样式化的输入容器
/**
 * 格式化日期显示
 */
const formatDate = (date: Date, format: string = 'YYYY-MM-DD'): string => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');

  switch (format) {
    case 'MM/DD/YYYY':
      return `${month}/${day}/${year}`;
    case 'DD/MM/YYYY':
      return `${day}/${month}/${year}`;
    default:
      return `${year}-${month}-${day}`;
  }
};

/**
 * 获取月份名称
 */
const getMonthName = (month: number): string => {
  const months = [
    '一月', '二月', '三月', '四月', '五月', '六月',
    '七月', '八月', '九月', '十月', '十一月', '十二月'
  ];
  return months[month];
};

/**
 * 获取月份的天数
 */
const getDaysInMonth = (year: number, month: number): number => {
  return new Date(year, month + 1, 0).getDate();
};

/**
 * M3E Date Picker 组件
 * 
 * 基于 Material Design 3 规范的日期选择器组件。
 * 
 * @example
 * ```tsx
 * const [date, setDate] = useState(new Date());
 * 
 * <M3EDatePicker
 *   value={date}
 *   onDateChange={setDate}
 *   label="选择日期"
 *   dateFormat="YYYY-MM-DD"
 * />
 * ```
 */
export const M3EDatePicker: React.FC<M3EDatePickerProps> = ({
  value,
  onDateChange,
  minimumDate,
  maximumDate,
  disabled = false,
  label,
  placeholder = '选择日期',
  dateFormat = 'YYYY-MM-DD',
  className = '',
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [selectedDate, setSelectedDate] = useState(value || new Date());

  const handlePress = () => {
    if (disabled) return;
    setIsVisible(true);
  };

  const handleConfirm = () => {
    onDateChange?.(selectedDate);
    setIsVisible(false);
  };

  const handleCancel = () => {
    setSelectedDate(value || new Date());
    setIsVisible(false);
  };

  const adjustDate = (type: 'year' | 'month' | 'day', delta: number) => {
    const newDate = new Date(selectedDate);
    
    if (type === 'year') {
      newDate.setFullYear(newDate.getFullYear() + delta);
    } else if (type === 'month') {
      newDate.setMonth(newDate.getMonth() + delta);
    } else {
      newDate.setDate(newDate.getDate() + delta);
    }

    // 检查日期范围
    if (minimumDate && newDate < minimumDate) return;
    if (maximumDate && newDate > maximumDate) return;

    setSelectedDate(newDate);
  };

  const displayValue = value ? formatDate(value, dateFormat) : placeholder;
  const containerClasses = `${className}`;

  return (
    <View className={containerClasses}>
      {/* 标签 */}
      {label && (
        <Text className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {label}
        </Text>
      )}

      {/* 输入框 */}
      <StyledInputContainer
        disabled={disabled}
        onPress={handlePress}
        className="border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 bg-white dark:bg-gray-800"
        activeOpacity={0.7}
      >
        <Text
          className={`text-base ${
            value
              ? 'text-gray-900 dark:text-white'
              : 'text-gray-500 dark:text-gray-400'
          }`}
        >
          {displayValue}
        </Text>
      </StyledInputContainer>

      {/* 日期选择器模态框 */}
      <Modal
        visible={isVisible}
        transparent
        animationType="fade"
        onRequestClose={handleCancel}
      >
        <View className="flex-1 bg-black/50 justify-center items-center p-4">
          <View className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-sm">
            {/* 标题 */}
            <Text className="text-xl font-medium text-gray-900 dark:text-white text-center mb-6">
              选择日期
            </Text>

            {/* 日期显示 */}
            <View className="items-center mb-6">
              <Text className="text-2xl font-light text-gray-900 dark:text-white">
                {formatDate(selectedDate, dateFormat)}
              </Text>
              <Text className="text-lg text-gray-600 dark:text-gray-400 mt-1">
                {getMonthName(selectedDate.getMonth())} {selectedDate.getFullYear()}
              </Text>
            </View>

            {/* 日期调整控件 */}
            <View className="flex-row justify-between items-center mb-8">
              {/* 年份调整 */}
              <View className="items-center flex-1">
                <TouchableOpacity
                  onPress={() => adjustDate('year', 1)}
                  className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-full items-center justify-center mb-2"
                >
                  <Text className="text-lg text-purple-600 dark:text-purple-400">+</Text>
                </TouchableOpacity>
                <Text className="text-sm font-medium text-gray-900 dark:text-white">
                  {selectedDate.getFullYear()}
                </Text>
                <TouchableOpacity
                  onPress={() => adjustDate('year', -1)}
                  className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-full items-center justify-center mt-2"
                >
                  <Text className="text-lg text-purple-600 dark:text-purple-400">-</Text>
                </TouchableOpacity>
              </View>

              {/* 月份调整 */}
              <View className="items-center flex-1">
                <TouchableOpacity
                  onPress={() => adjustDate('month', 1)}
                  className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-full items-center justify-center mb-2"
                >
                  <Text className="text-lg text-purple-600 dark:text-purple-400">+</Text>
                </TouchableOpacity>
                <Text className="text-sm font-medium text-gray-900 dark:text-white">
                  {selectedDate.getMonth() + 1}
                </Text>
                <TouchableOpacity
                  onPress={() => adjustDate('month', -1)}
                  className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-full items-center justify-center mt-2"
                >
                  <Text className="text-lg text-purple-600 dark:text-purple-400">-</Text>
                </TouchableOpacity>
              </View>

              {/* 日期调整 */}
              <View className="items-center flex-1">
                <TouchableOpacity
                  onPress={() => adjustDate('day', 1)}
                  className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-full items-center justify-center mb-2"
                >
                  <Text className="text-lg text-purple-600 dark:text-purple-400">+</Text>
                </TouchableOpacity>
                <Text className="text-sm font-medium text-gray-900 dark:text-white">
                  {selectedDate.getDate()}
                </Text>
                <TouchableOpacity
                  onPress={() => adjustDate('day', -1)}
                  className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-full items-center justify-center mt-2"
                >
                  <Text className="text-lg text-purple-600 dark:text-purple-400">-</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* 操作按钮 */}
            <View className="flex-row justify-end gap-4">
              <TouchableOpacity
                onPress={handleCancel}
                className="px-6 py-2 rounded-full"
              >
                <Text className="text-purple-600 dark:text-purple-400 font-medium">
                  取消
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={handleConfirm}
                className="px-6 py-2 bg-purple-600 rounded-full"
              >
                <Text className="text-white font-medium">确认</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default M3EDatePicker;
