import React from 'react';
import { useTranslation } from 'react-i18next';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';

export function EmptyState() {
  const { t } = useTranslation();

  return (
    <Box className="flex-1 justify-center items-center p-4 bg-background-0">
      <Text className="text-base font-medium text-error-600 text-center">
        {t('storyDetail.errors.notFound', 'Story not found.')}
      </Text>
    </Box>
  );
}
