import React from 'react';
import { Conversation } from '@/api/messages/types';
import { formatDistanceToNow } from 'date-fns';
import { zhCN, enUS } from 'date-fns/locale';
import { useTranslation } from 'react-i18next';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Image } from '@/components/ui/image';
import { Pressable } from '@/components/ui/pressable';

interface ConversationItemProps {
  conversation: Conversation;
  onPress: (conversation: Conversation) => void;
}

export default function ConversationItem({
  conversation,
  onPress,
}: ConversationItemProps) {
  const { t, i18n } = useTranslation();

  // 格式化时间
  const formatTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, {
        addSuffix: true,
        locale: i18n.language === 'zh' ? zhCN : enUS,
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  // 处理对话点击
  const handlePress = () => {
    onPress(conversation);
  };

  // 获取对话的另一个参与者
  const otherParticipant = conversation.other_participant;

  const hasUnread = conversation.unread_count && conversation.unread_count > 0;

  return (
    <Pressable
      className={`flex-row p-4 border-b border-outline-300 ${
        hasUnread ? 'bg-background-100' : 'bg-background-0'
      }`}
      onPress={handlePress}
    >
      {otherParticipant && (
        <Box className="relative mr-4">
          {otherParticipant.avatar_url ? (
            <Image
              source={{ uri: otherParticipant.avatar_url }}
              className="w-[50px] h-[50px] rounded-full"
              alt={otherParticipant.username || 'User avatar'}
            />
          ) : (
            <Box className="w-[50px] h-[50px] rounded-full bg-background-300" />
          )}
          {hasUnread && (
            <Box className="absolute top-0 right-0 min-w-[18px] h-[18px] rounded-full bg-error-500 justify-center items-center px-1">
              <Text className="text-2xs text-background-0 font-medium">
                {conversation.unread_count > 99
                  ? '99+'
                  : conversation.unread_count}
              </Text>
            </Box>
          )}
        </Box>
      )}

      <Box className="flex-1 justify-center">
        <Box className="flex-row justify-between items-center mb-1">
          <Text
            className="text-base font-medium text-typography-900 flex-1 mr-2"
            numberOfLines={1}
          >
            {otherParticipant?.display_name ||
              otherParticipant?.username ||
              t('messages.unknownUser', '未知用户')}
          </Text>
          <Text className="text-xs text-typography-500">
            {formatTime(conversation.last_message_at)}
          </Text>
        </Box>

        <Text className="text-sm text-typography-600" numberOfLines={1}>
          {conversation.last_message || t('messages.noMessages', '暂无消息')}
        </Text>
      </Box>
    </Pressable>
  );
}
