import React from 'react';
import { Box } from '@/components/ui/box';
import { Button, ButtonText, ButtonIcon } from '@/components/ui/m3e-button';
import { useTheme } from '@/lib/theme/theme-provider';
import { ArrowRight } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';

interface CreateScreenFooterProps {
  onNextPress: () => void;
  canProceed: boolean;
  nextButtonText?: string; // Allow customizing button text
}

export function CreateScreenFooter({
  onNextPress,
  canProceed,
  nextButtonText,
}: CreateScreenFooterProps) {
  const { theme } = useTheme();
  const { t } = useTranslation();

  const buttonText = nextButtonText || t('common.next', '下一步');

  return (
    <Box className="p-4 border-t border-border bg-background-0 dark:bg-background-900">
      <Button
        action="primary"
        variant="solid"
        size="md"
        onPress={onNextPress}
        isDisabled={!canProceed}
        className="flex-row items-center justify-center py-3 rounded-xl"
        opacity={canProceed ? 1 : 0.5}
      >
        <ButtonText>{buttonText}</ButtonText>
        <ButtonIcon as={ArrowRight} size={20} className="ml-1" />
      </Button>
    </Box>
  );
}
