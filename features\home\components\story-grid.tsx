import React from 'react';
import { Box } from '@/components/ui/box';
import StoryC<PERSON>, { Story } from '@/components/stories/story-card'; // Use existing card

interface StoryGridProps {
  stories: Story[];
  onStoryPress?: (storyId: string) => void;
}

export function StoryGrid({ stories, onStoryPress }: StoryGridProps) {
  return (
    <Box className="flex-row flex-wrap justify-between mt-2">
      {stories.map(story => (
        <StoryCard 
          key={story.id} 
          story={story} 
          onPress={() => onStoryPress?.(story.id)}
        />
      ))}
      {/* Add Empty state or Loading state if needed */}
    </Box>
  );
}
