import { useState } from 'react';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { getAISuggestions } from '@/api/ai/story-generation';

interface UseAISuggestionsProps {
  getPrompt: () => string;
  onSelectSuggestion: (suggestion: string) => void;
}

export function useAISuggestions({
  getPrompt,
  onSelectSuggestion,
}: UseAISuggestionsProps) {
  const { t } = useTranslation();

  const [aiSuggestions, setAiSuggestions] = useState<string[]>([]);
  const [loadingAISuggestions, setLoadingAISuggestions] = useState(false);
  const [showAISuggestions, setShowAISuggestions] = useState(false);

  const handleFetchAISuggestions = async () => {
    const prompt = getPrompt();

    if (!prompt.trim()) {
      Alert.alert(
        t('aiSuggestions.promptErrorTitle', '??'),
        t(
          'aiSuggestions.promptError',
          '??????????????? AI ????'
        )
      );
      return;
    }

    setShowAISuggestions(true);
    setLoadingAISuggestions(true);
    setAiSuggestions([]); // Clear previous suggestions

    try {
      const response = await getAISuggestions({ prompt });

      if (response.error) {
        setAiSuggestions([]);
        Alert.alert(
          t('aiSuggestions.fetchErrorTitle', '??????'),
          response.error
        );
      } else if (response.suggestions && response.suggestions.length > 0) {
        setAiSuggestions(response.suggestions);
      } else {
        setAiSuggestions([]);
        Alert.alert(
          t('aiSuggestions.fetchErrorTitle', '??????'),
          t(
            'aiSuggestions.noSuggestions',
            '???????????????????????'
          )
        );
      }
    } catch (error) {
      console.error('Error fetching AI suggestions:', error);
      setAiSuggestions([]);
      Alert.alert(
        t('aiSuggestions.fetchErrorTitle', '??????'),
        error.message || t('aiSuggestions.fetchError', '?? AI ???????')
      );
    } finally {
      setLoadingAISuggestions(false);
    }
  };

  const handleSelectAISuggestion = (suggestion: string) => {
    onSelectSuggestion(suggestion);
    setShowAISuggestions(false);
    setAiSuggestions([]);
  };

  return {
    aiSuggestions,
    loadingAISuggestions,
    showAISuggestions,
    handleFetchAISuggestions,
    handleSelectAISuggestion,
  };
}
