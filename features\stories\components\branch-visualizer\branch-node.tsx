import React from 'react';
import { StorySegment } from '@/api/stories';
import { BranchNode } from '@/api/stories/branches/types';
import { useTranslation } from 'react-i18next';
import { ChevronRight, ChevronDown, GitBranch } from 'lucide-react-native';
import BranchActionsMenu from '../branch-actions-menu';

// Import gluestack-ui components
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';

interface BranchNodeProps {
  node: BranchNode;
  depth: number;
  isExpanded: boolean;
  isSelected: boolean;
  hasChildren: boolean;
  collapsible: boolean;
  isAuthor: boolean;
  onToggle: (nodeId: string) => void;
  onSelect: (nodeId: string) => void;
  onRenameBranch?: (
    segmentId: string,
    branchTitle: string
  ) => Promise<StorySegment | null>;
  onDeleteBranch?: (segmentId: string) => Promise<boolean>;
}

export function BranchNode({
  node,
  depth,
  isExpanded,
  isSelected,
  hasChildren,
  collapsible,
  isAuthor,
  onToggle,
  onSelect,
  onRenameBranch,
  onDeleteBranch,
}: BranchNodeProps) {
  const { t } = useTranslation();

  // 提取分支标题（如果有）
  const extractBranchTitle = (content: string): string => {
    const titleMatch = content.match(/^\[(.*?)\]\s/);
    return titleMatch ? titleMatch[1] : '';
  };

  // 获取显示的分支标题
  const displayTitle =
    node.branchTitle ||
    extractBranchTitle(node.segment.content) ||
    t('storyDetail.branch', 'Branch');

  // 获取显示的分支内容预览
  const getContentPreview = (content: string): string => {
    // 如果内容有标题格式，移除标题部分
    const titleRegex = /^\[(.*?)\]\s/;
    const contentWithoutTitle = content.replace(titleRegex, '');
    return (
      contentWithoutTitle.substring(0, 50) +
      (contentWithoutTitle.length > 50 ? '...' : '')
    );
  };

  return (
    <Box
      style={{ marginLeft: depth * 20 }}
      className="flex-row items-center my-0.5"
    >
      <Pressable
        className={`flex-1 flex-row items-center p-2 rounded-md border ${
          isSelected
            ? 'bg-primary-100 dark:bg-primary-900 border-primary-500 dark:border-primary-400'
            : 'bg-surface-50 dark:bg-surface-900 border-outline-200 dark:border-outline-700'
        }`}
        onPress={() => {
          if (node.id) {
            onSelect(node.id);
          }
        }}
      >
        {hasChildren && collapsible && (
          <Pressable
            className="w-6 h-6 justify-center items-center mr-1"
            onPress={() => onToggle(node.id)}
          >
            {isExpanded ? (
              <ChevronDown
                size={16}
                className="text-typography-900 dark:text-typography-100"
              />
            ) : (
              <ChevronRight
                size={16}
                className="text-typography-900 dark:text-typography-100"
              />
            )}
          </Pressable>
        )}

        <GitBranch
          size={16}
          className={
            isSelected
              ? 'text-primary-500 dark:text-primary-400'
              : 'text-typography-900 dark:text-typography-100'
          }
          style={{ marginRight: 8 }}
        />

        <Box className="flex-1 justify-center">
          <Text
            className={`text-sm truncate ${
              isSelected
                ? 'text-primary-600 dark:text-primary-400 font-bold'
                : 'text-typography-900 dark:text-typography-100 font-medium'
            }`}
          >
            {displayTitle}{' '}
            {depth > 0 ? `${depth}.${node.segment.order_in_branch + 1}` : ''}
          </Text>

          <Text className="text-xs text-secondary-500 dark:text-secondary-400 mt-0.5 truncate">
            {getContentPreview(node.segment.content)}
          </Text>
        </Box>
      </Pressable>

      {/* 分支操作菜单 */}
      {onRenameBranch && onDeleteBranch && (
        <BranchActionsMenu
          segment={node.segment}
          onRename={onRenameBranch}
          onDelete={onDeleteBranch}
          isAuthor={isAuthor}
        />
      )}
    </Box>
  );
}
