import React from 'react';
import { useTranslation } from 'react-i18next';
import { StoryWithSegments } from '@/api/stories';
import { Heart } from 'lucide-react-native';

// Import gluestack-ui components
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Image } from '@/components/ui/image';
import { Button } from '@/components/ui/m3e-button';
import { ButtonText } from '@/components/ui/m3e-button';
import { ButtonIcon } from '@/components/ui/m3e-button';
import { ButtonSpinner } from '@/components/ui/m3e-button';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';

interface StoryDetailHeaderProps {
  story: StoryWithSegments; // Use the full story object for now, can be optimized later
  isLiked: boolean;
  likeCount: number;
  onLikeToggle: () => void;
  isLiking: boolean;
}

export default function StoryDetailHeader({
  story,
  isLiked,
  likeCount,
  onLikeToggle,
  isLiking,
}: StoryDetailHeaderProps) {
  const { t } = useTranslation();

  if (!story) return null; // Should not happen if parent component handles loading

  return (
    <VStack className="items-center mb-6">
      {story.cover_image_url && (
        <Image
          source={{ uri: story.cover_image_url }}
          alt={story.title}
          className="w-full h-64 rounded-xl mb-4 bg-surface-100 dark:bg-surface-800"
          resizeMode="cover"
        />
      )}
      <Text className="text-2xl font-bold text-typography-900 dark:text-typography-100 text-center mb-1">
        {story.title}
      </Text>
      <Text className="text-base text-typography-700 dark:text-typography-300 mb-3">
        {t('storyDetail.by', 'By')}{' '}
        {story.profiles?.username ||
          t('storyDetail.unknownAuthor', 'Unknown Author')}
      </Text>

      {story.tags && story.tags.length > 0 && (
        <HStack className="flex-wrap justify-center gap-2 mb-4">
          {story.tags.map((tag, index) => (
            <Box
              key={index}
              className="px-2 py-1 rounded-md bg-primary-100 dark:bg-primary-900"
            >
              <Text className="text-xs text-primary-600 dark:text-primary-400">
                {tag}
              </Text>
            </Box>
          ))}
        </HStack>
      )}

      <Box className="mt-2">
        <Button
          onPress={onLikeToggle}
          isDisabled={isLiking}
          variant={isLiked ? 'solid' : 'outline'}
          action={isLiked ? 'primary' : 'secondary'}
          className="flex-row items-center px-4 py-2 rounded-full"
        >
          <ButtonIcon
            as={Heart}
            size="sm"
            fill={isLiked ? 'currentColor' : 'none'}
          />
          <ButtonText className="ml-1">
            {likeCount}{' '}
            {likeCount === 1
              ? t('storyDetail.like', 'Like')
              : t('storyDetail.likes', 'Likes')}
          </ButtonText>
          {isLiking && <ButtonSpinner className="ml-1" />}
        </Button>
      </Box>
    </VStack>
  );
}
