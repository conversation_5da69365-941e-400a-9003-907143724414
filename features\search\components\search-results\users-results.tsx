import React from 'react';
import { User } from '@/types/user';
import UserCard from '@/components/social/user-card';
import { Box } from '@/components/ui/box';

interface UsersResultsProps {
  users: User[];
  onUserPress: (userId: string) => void;
  limit?: number;
}

export function UsersResults({ users, onUserPress, limit }: UsersResultsProps) {
  const displayUsers = limit ? users.slice(0, limit) : users;

  return (
    <Box className="flex-row flex-wrap p-2">
      {displayUsers.map((user) => (
        <UserCard
          key={user.id}
          user={user}
          onPress={onUserPress}
          onFollow={() => {}} // Follow functionality not implemented yet
          isFollowing={false}
        />
      ))}
    </Box>
  );
}
