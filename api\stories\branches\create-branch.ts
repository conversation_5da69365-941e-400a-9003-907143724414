import { supabase } from '@/utils/supabase';
import { PostgrestError } from '@supabase/supabase-js';
import { StorySegment } from '../types';

/**
 * 创建新分支
 *
 * @param storyId 故事ID
 * @param parentSegmentId 父段落ID
 * @param content 分支内容
 * @param branchTitle 分支标题（可选）
 * @param isAiGenerated 是否由AI生成
 * @returns 新创建的段落
 */
export async function createBranch(
  storyId: string,
  parentSegmentId: string,
  content: string,
  branchTitle?: string,
  isAiGenerated: boolean = false
): Promise<{ data: StorySegment | null; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return {
      data: null,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };
  }

  try {
    // 获取同级分支数量，用于设置order_in_branch
    const { data: siblings, error: countError } = await supabase
      .from('story_segments')
      .select('id')
      .eq('parent_segment_id', parentSegmentId);

    if (countError) {
      throw countError;
    }

    const orderInBranch = siblings ? siblings.length : 0;

    // 创建新分支段落
    const { data, error } = await supabase
      .from('story_segments')
      .insert({
        story_id: storyId,
        author_id: user.id,
        content,
        content_type: 'text',
        parent_segment_id: parentSegmentId,
        order_in_branch: orderInBranch,
        is_ai_generated: isAiGenerated,
        // 如果有分支标题，可以存储在元数据中
        // 这里假设数据库中有metadata字段，如果没有，需要先添加该字段
        // metadata: branchTitle ? { branchTitle } : null,
      })
      .select(
        `
        id, story_id, author_id, content_type, content, parent_segment_id,
        order_in_branch, is_ai_generated, created_at, updated_at,
        likes_count, dislikes_count, bookmarks_count, comment_count,
        profiles!story_segments_author_id_fkey ( id, username, avatar_url )
      `
      )
      .single();

    if (error) {
      throw error;
    }

    return { data: data as StorySegment, error: null };
  } catch (error) {
    console.error('Error creating branch:', error);
    return {
      data: null,
      error: error as PostgrestError,
    };
  }
}
