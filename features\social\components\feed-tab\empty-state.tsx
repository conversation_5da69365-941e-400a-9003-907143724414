import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/m3e-button';
import { ButtonText } from '@/components/ui/m3e-button';
import { useTranslation } from 'react-i18next';
import { Users } from 'lucide-react-native';
import { useColorScheme } from 'nativewind';

interface EmptyStateProps {
  onDiscoverPress?: () => void;
}

export function EmptyState({ onDiscoverPress }: EmptyStateProps) {
  const { t } = useTranslation();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  return (
    <VStack className="flex-1 justify-center items-center px-8 min-h-[400px]">
      <Users
        size={60}
        className={`mb-6 opacity-70 ${
          isDark ? 'text-typography-400' : 'text-typography-500'
        }`}
      />
      <Text
        className={`text-lg font-bold mb-2 text-center ${
          isDark ? 'text-typography-50' : 'text-typography-900'
        }`}
      >
        {t('social.feed.emptyStateTitle', '暂无动态')}
      </Text>
      <Text
        className={`text-base text-center mb-6 ${
          isDark ? 'text-typography-400' : 'text-typography-500'
        }`}
      >
        {t(
          'social.feed.empty',
          '暂无动态。关注更多用户以在此处查看他们的故事。'
        )}
      </Text>
      <Button
        size="md"
        variant="solid"
        action="primary"
        onPress={onDiscoverPress}
      >
        <ButtonText>{t('social.feed.discoverUsers', '发现用户')}</ButtonText>
      </Button>
    </VStack>
  );
}
