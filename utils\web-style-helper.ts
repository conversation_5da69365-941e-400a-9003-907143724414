import React from 'react';

/**
 * 处理 React Native 样式对象，使其在 Web 平台上兼容
 * 主要解决数组样式和特定平台样式的问题
 */
export function processWebStyle(style: any): any {
  if (!style) return style;

  // 如果是数组，将其合并为单个对象
  if (Array.isArray(style)) {
    return style.reduce((result, current) => {
      if (!current) return result;
      return { ...result, ...processWebStyle(current) };
    }, {});
  }

  // 处理对象样式
  const result: Record<string, any> = {};

  for (const key in style) {
    const value = style[key];

    // 递归处理嵌套样式对象
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      result[key] = processWebStyle(value);
    } else {
      result[key] = value;
    }
  }

  return result;
}

/**
 * 创建一个 Web 安全的样式对象
 * 用于替换 style={[styles.a, styles.b]} 这样的用法
 */
export function createWebStyle(...styles: any[]): any {
  return processWebStyle(styles);
}

/**
 * 处理 onLayout 事件
 * 在 Web 平台上，使用 ResizeObserver 模拟 onLayout 事件
 */
export function useWebLayout(
  ref: React.RefObject<HTMLElement>,
  onLayout?: (event: {
    nativeEvent: {
      layout: { width: number; height: number; x: number; y: number };
    };
  }) => void
) {
  React.useEffect(() => {
    if (!onLayout || !ref.current) return;

    // 使用 ResizeObserver 监听元素大小变化
    const observer = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        const { x, y } = ref.current?.getBoundingClientRect() || { x: 0, y: 0 };

        onLayout({
          nativeEvent: {
            layout: { width, height, x, y },
          },
        });
      }
    });

    observer.observe(ref.current);

    // 清理函数
    return () => {
      observer.disconnect();
    };
  }, [ref, onLayout]);
}
