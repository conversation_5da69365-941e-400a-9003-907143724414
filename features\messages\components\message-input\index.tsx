import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Send } from 'lucide-react-native';
import { Box } from '@/components/ui/box';
import { TextArea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/m3e-button';
import { ButtonIcon } from '@/components/ui/m3e-button';
import { Spinner } from '@/components/ui/spinner';

interface MessageInputProps {
  onSend: (message: string) => Promise<void>;
  placeholder?: string;
}

export default function MessageInput({
  onSend,
  placeholder,
}: MessageInputProps) {
  const { t } = useTranslation();
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);

  const handleSend = async () => {
    if (message.trim() === '' || isSending) {
      return;
    }

    setIsSending(true);
    try {
      await onSend(message);
      setMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsSending(false);
    }
  };

  const isDisabled = message.trim() === '' || isSending;

  return (
    <Box className="flex-row items-center px-4 py-2 border-t border-outline-300 bg-background-0">
      <TextArea
        className="flex-1 min-h-[40px] max-h-[100px] bg-background-100 rounded-md px-4 py-2 mr-4 text-base text-typography-900"
        placeholder={placeholder || t('messages.typeSomething', '输入消息...')}
        value={message}
        onChangeText={setMessage}
        multiline
        maxLength={1000}
        onSubmitEditing={handleSend}
      />

      <Button
        className={`w-10 h-10 rounded-full justify-center items-center ${
          isDisabled ? 'bg-background-400' : 'bg-primary-500'
        }`}
        onPress={handleSend}
        isDisabled={isDisabled}
      >
        {isSending ? (
          <Spinner size="small" color="white" />
        ) : (
          <ButtonIcon as={Send} size="md" color="white" />
        )}
      </Button>
    </Box>
  );
}
