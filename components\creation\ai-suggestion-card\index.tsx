import React from 'react';
import { Pressable } from '@/components/ui/pressable';
import { Text } from '@/components/ui/text';
import { HStack } from '@/components/ui/hstack';
import { Sparkles } from 'lucide-react-native';

interface AiSuggestionCardProps {
  suggestion: string;
  onSelect: (text: string) => void;
}

export default function AiSuggestionCard({
  suggestion,
  onSelect,
}: AiSuggestionCardProps) {
  return (
    <Pressable
      className="p-4 rounded-md border border-outline-200 bg-background-50 dark:border-outline-700 dark:bg-background-800"
      onPress={() => onSelect(suggestion)}
    >
      <HStack className="items-center">
        <Sparkles size={16} className="text-primary-500 mr-2" />
        <Text
          size="md"
          className="font-medium text-typography-900 dark:text-typography-50 flex-1 truncate"
        >
          {suggestion}
        </Text>
      </HStack>
    </Pressable>
  );
}
