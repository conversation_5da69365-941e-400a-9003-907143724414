import { useState, useEffect, useCallback } from 'react';
import {
  getConversations,
  getUnreadMessageCount,
  createConversation,
  markConversationAsRead,
} from '@/api/messages';
import { Conversation, CreateConversationOptions } from '@/api/messages/types';

interface UseConversationsProps {
  initialLimit?: number;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export function useConversations({
  initialLimit = 20,
  autoRefresh = false,
  refreshInterval = 60000, // 1分钟
}: UseConversationsProps = {}) {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [offset, setOffset] = useState(0);
  const [limit] = useState(initialLimit);

  // 获取对话列表
  const fetchConversations = useCallback(async (reset = false) => {
    setIsLoading(true);
    setError(null);

    try {
      const newOffset = reset ? 0 : offset;
      const options = {
        limit,
        offset: newOffset,
      };

      const { data, error } = await getConversations(options);

      if (error) {
        throw new Error(error.message);
      }

      if (data) {
        if (reset) {
          setConversations(data);
        } else {
          setConversations((prev) => [...prev, ...data]);
        }
        setHasMore(data.length === limit);
        setOffset(reset ? limit : newOffset + limit);
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch conversations'));
    } finally {
      setIsLoading(false);
    }
  }, [offset, limit]);

  // 获取未读消息数量
  const fetchUnreadCount = useCallback(async () => {
    try {
      const { count, error } = await getUnreadMessageCount();
      if (error) {
        throw new Error(error.message);
      }
      setUnreadCount(count);
    } catch (err) {
      console.error('Error fetching unread count:', err);
    }
  }, []);

  // 创建新对话
  const startConversation = useCallback(async (options: CreateConversationOptions) => {
    try {
      const { data, error } = await createConversation(options);
      if (error) {
        throw new Error(error.message);
      }
      if (data) {
        setConversations((prev) => [data, ...prev]);
      }
      return data;
    } catch (err) {
      console.error('Error creating conversation:', err);
      return null;
    }
  }, []);

  // 标记对话为已读
  const markAsRead = useCallback(async (conversationId: string) => {
    try {
      const { success, error } = await markConversationAsRead(conversationId);
      if (error) {
        throw new Error(error.message);
      }
      if (success) {
        setConversations((prev) =>
          prev.map((conversation) =>
            conversation.id === conversationId
              ? { ...conversation, unread_count: 0 }
              : conversation
          )
        );
        fetchUnreadCount();
      }
      return success;
    } catch (err) {
      console.error('Error marking conversation as read:', err);
      return false;
    }
  }, [fetchUnreadCount]);

  // 刷新对话列表
  const refreshConversations = useCallback(() => {
    fetchConversations(true);
    fetchUnreadCount();
  }, [fetchConversations, fetchUnreadCount]);

  // 加载更多对话
  const loadMoreConversations = useCallback(() => {
    if (!isLoading && hasMore) {
      fetchConversations();
    }
  }, [isLoading, hasMore, fetchConversations]);

  // 初始加载
  useEffect(() => {
    fetchConversations(true);
    fetchUnreadCount();
  }, [fetchConversations, fetchUnreadCount]);

  // 自动刷新
  useEffect(() => {
    if (autoRefresh) {
      const intervalId = setInterval(() => {
        fetchUnreadCount();
      }, refreshInterval);

      return () => clearInterval(intervalId);
    }
  }, [autoRefresh, refreshInterval, fetchUnreadCount]);

  return {
    conversations,
    unreadCount,
    isLoading,
    error,
    hasMore,
    startConversation,
    markAsRead,
    refreshConversations,
    loadMoreConversations,
  };
}