import React, { useEffect } from 'react';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { getUnreadMessageCount } from '@/api/messages';

interface MessageBadgeProps {
  count?: number;
  size?: 'small' | 'medium' | 'large';
  autoRefresh?: boolean;
  refreshInterval?: number;
  onCountChange?: (count: number) => void;
}

export default function MessageBadge({
  count: propCount,
  size = 'medium',
  autoRefresh = false,
  refreshInterval = 60000, // 1分钟
  onCountChange,
}: MessageBadgeProps) {
  const [count, setCount] = React.useState(propCount || 0);

  // 如果外部传入了 count，使用外部的 count
  useEffect(() => {
    if (propCount !== undefined) {
      setCount(propCount);
    }
  }, [propCount]);

  // 如果没有外部传入 count，自动获取未读消息数量
  useEffect(() => {
    if (propCount === undefined) {
      fetchUnreadCount();
    }
  }, [propCount]);

  // 自动刷新
  useEffect(() => {
    if (autoRefresh && propCount === undefined) {
      const intervalId = setInterval(() => {
        fetchUnreadCount();
      }, refreshInterval);

      return () => clearInterval(intervalId);
    }
  }, [autoRefresh, refreshInterval, propCount]);

  // 获取未读消息数量
  const fetchUnreadCount = async () => {
    try {
      const { count: unreadCount, error } = await getUnreadMessageCount();
      if (error) {
        throw new Error(error.message);
      }
      setCount(unreadCount);
      onCountChange?.(unreadCount);
    } catch (err) {
      console.error('Error fetching unread count:', err);
    }
  };

  // 如果没有未读消息，不显示徽章
  if (count === 0) {
    return null;
  }

  // 根据 size 获取样式
  const getBadgeSize = () => {
    switch (size) {
      case 'small':
        return {
          minWidth: 'min-w-4',
          height: 'h-4',
          padding: 'px-0.5',
          fontSize: 'text-2xs',
        };
      case 'large':
        return {
          minWidth: 'min-w-6',
          height: 'h-6',
          padding: 'px-1.5',
          fontSize: 'text-sm',
        };
      case 'medium':
      default:
        return {
          minWidth: 'min-w-5',
          height: 'h-5',
          padding: 'px-1',
          fontSize: 'text-xs',
        };
    }
  };

  // 格式化数量
  const formatCount = (count: number) => {
    if (count > 99) {
      return '99+';
    }
    return count.toString();
  };

  const sizeStyles = getBadgeSize();

  return (
    <Box
      className={`absolute -top-1 -right-1 bg-error-500 rounded-full justify-center items-center z-10 ${sizeStyles.minWidth} ${sizeStyles.height} ${sizeStyles.padding}`}
    >
      <Text
        className={`text-background-0 font-medium text-center ${sizeStyles.fontSize}`}
      >
        {formatCount(count)}
      </Text>
    </Box>
  );
}
