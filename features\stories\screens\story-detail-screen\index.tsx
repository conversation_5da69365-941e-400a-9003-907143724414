import React, { useState, useEffect, useCallback } from 'react';
import { FlatList } from 'react-native';
import { useTranslation } from 'react-i18next';
import { StorySegment } from '@/api/stories';

// Hooks
import { useStoryDetails } from '../../hooks/use-story-details';
import { useStoryAISuggestions } from '../../hooks/use-story-aisuggestions';
import { useStorySegmentManagement } from '../../hooks/use-story-segment-management';
import { useStoryOptimization } from '../../hooks/use-story-optimization';

// Components
import StoryDetailHeader from '../../components/story-detail-header';
import StorySegmentItem from '../../components/story-segment-item';
import AddSegmentForm from '../../components/add-segment-form';
import AISuggestionBlock from '../../components/aisuggestion-block';
import StoryOptimizationBlock from '../../components/story-optimization-block';
import { LoadingState } from './loading-state';
import { ErrorState } from './error-state';
import { EmptyState } from './empty-state';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { ScrollView } from '@/components/ui/scroll-view';

interface StoryDetailScreenProps {
  storyId: string;
}

export default function StoryDetailScreen({ storyId }: StoryDetailScreenProps) {
  const { t } = useTranslation();

  const {
    story,
    isLoading: isLoadingDetails,
    error: storyError,
    isLiked,
    likeCount,
    isLiking,
    fetchStoryDetails,
    handleLikeToggle,
  } = useStoryDetails(storyId);

  const [segments, setSegments] = useState<StorySegment[]>([]);

  useEffect(() => {
    if (story?.story_segments) {
      setSegments(story.story_segments);
    }
  }, [story?.story_segments]);

  const handleSegmentAdded = useCallback((newSegment: StorySegment) => {
    setSegments((prevSegments) => [...prevSegments, newSegment]);
  }, []);

  const {
    newSegmentContent,
    setNewSegmentContent,
    isSubmittingSegment,
    handleAddSegment,
  } = useStorySegmentManagement({
    storyId,
    onSegmentAdded: handleSegmentAdded,
  });

  const {
    aiSegmentSuggestions,
    loadingAISegmentSuggestions,
    showAISegmentSuggestions,
    currentSegmentIsAI,
    setCurrentSegmentIsAI,
    handleFetchAISuggestionsForSegment,
    handleSelectAISuggestion,
  } = useStoryAISuggestions(story);

  const {
    isOptimizing,
    optimizationType,
    setOptimizationType,
    handleOptimizeContent,
  } = useStoryOptimization({
    onOptimizedContent: (optimizedContent) => {
      setNewSegmentContent(optimizedContent);
      setCurrentSegmentIsAI(true); // Mark as AI-assisted
    },
  });

  const onSelectAISuggestionForForm = (suggestion: string) => {
    handleSelectAISuggestion(suggestion, setNewSegmentContent);
  };

  const submitSegment = () => {
    handleAddSegment(currentSegmentIsAI);
    setCurrentSegmentIsAI(false); // Reset AI flag
  };

  const handleOptimize = (content: string, type: any) => {
    setOptimizationType(type);
    handleOptimizeContent(content);
  };

  if (isLoadingDetails) {
    return <LoadingState />;
  }

  if (storyError) {
    return <ErrorState errorMessage={storyError} onRetry={fetchStoryDetails} />;
  }

  if (!story) {
    return <EmptyState />;
  }

  return (
    <ScrollView
      className="flex-1 bg-background-0"
      contentContainerStyle={{ padding: 16, paddingBottom: 32 }}
      keyboardShouldPersistTaps="handled"
    >
      <StoryDetailHeader
        story={story}
        isLiked={isLiked}
        likeCount={likeCount}
        onLikeToggle={handleLikeToggle}
        isLiking={isLiking}
      />

      <Box className="mt-6 mb-6">
        <Text className="text-lg font-bold text-typography-900 mb-4">
          {t('storyDetail.storyContent', 'Story Content')}
        </Text>
        {segments && segments.length > 0 ? (
          <FlatList
            data={segments}
            renderItem={({ item, index }) => (
              <StorySegmentItem
                segment={item}
                isFirst={index === 0}
                isLast={index === segments.length - 1}
                showDivider={index > 0}
              />
            )}
            keyExtractor={(item) => item.id.toString()}
            scrollEnabled={false} // Important if inside a ScrollView
          />
        ) : (
          <Text className="text-base text-typography-600 text-center py-6 italic">
            {t(
              'storyDetail.noContent',
              'This story has no content yet. Be the first to add to it!'
            )}
          </Text>
        )}
      </Box>

      <AddSegmentForm
        segmentContent={newSegmentContent}
        onContentChange={setNewSegmentContent}
        onSubmit={submitSegment}
        isSubmitting={isSubmittingSegment}
      />

      <AISuggestionBlock
        onFetchSuggestions={handleFetchAISuggestionsForSegment}
        loadingSuggestions={loadingAISegmentSuggestions}
        showSuggestions={showAISegmentSuggestions}
        suggestions={aiSegmentSuggestions}
        onSelectSuggestion={onSelectAISuggestionForForm}
        isProcessingSegment={isSubmittingSegment}
      />

      <StoryOptimizationBlock
        onOptimizeContent={handleOptimize}
        isOptimizing={isOptimizing}
        disabled={isSubmittingSegment}
      />
    </ScrollView>
  );
}
