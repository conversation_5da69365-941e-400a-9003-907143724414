import { supabase } from '@/utils/supabase';
import { PostgrestError } from '@supabase/supabase-js';
import { StorySegment } from '../types';

/**
 * 重命名分支（更新分支元数据）
 *
 * @param segmentId 段落ID
 * @param branchTitle 分支标题
 * @returns 更新后的段落
 */
export async function renameBranch(
  segmentId: string,
  branchTitle: string
): Promise<{ data: StorySegment | null; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return {
      data: null,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };
  }

  try {
    // 首先检查段落是否存在并且用户是否有权限修改
    const { data: segment, error: checkError } = await supabase
      .from('story_segments')
      .select('id, author_id, story_id')
      .eq('id', segmentId)
      .single();

    if (checkError || !segment) {
      throw checkError || new Error('Segment not found');
    }

    // 检查用户是否是段落的作者
    if (segment.author_id !== user.id) {
      return {
        data: null,
        error: {
          message: 'Permission denied: You can only rename your own branches',
          details: '',
          hint: '',
          code: '403',
        } as PostgrestError,
      };
    }

    // 更新段落的元数据
    // 注意：这里假设数据库中有metadata字段，如果没有，需要先添加该字段
    // 这里我们使用一个临时解决方案，将分支标题存储在content字段的开头，格式为：[分支标题] 内容
    // 在实际项目中，应该添加一个专门的metadata字段或branch_title字段

    // 获取当前内容
    const { data: currentSegment, error: getError } = await supabase
      .from('story_segments')
      .select('content')
      .eq('id', segmentId)
      .single();

    if (getError || !currentSegment) {
      throw getError || new Error('Failed to get current segment content');
    }

    // 检查内容是否已经有标题格式
    const titleRegex = /^\[.*?\]\s/;
    let newContent = currentSegment.content;

    if (titleRegex.test(newContent)) {
      // 如果已经有标题，替换它
      newContent = newContent.replace(titleRegex, `[${branchTitle}] `);
    } else {
      // 如果没有标题，添加一个
      newContent = `[${branchTitle}] ${newContent}`;
    }

    // 更新内容
    const { data, error } = await supabase
      .from('story_segments')
      .update({ content: newContent })
      .eq('id', segmentId)
      .select(
        `
        id, story_id, author_id, content_type, content, parent_segment_id,
        order_in_branch, is_ai_generated, created_at, updated_at,
        likes_count, dislikes_count, bookmarks_count, comment_count,
        profiles!story_segments_author_id_fkey ( id, username, avatar_url )
      `
      )
      .single();

    if (error) {
      throw error;
    }

    return { data: data as StorySegment, error: null };
  } catch (error) {
    console.error('Error renaming branch:', error);
    return {
      data: null,
      error: error as PostgrestError,
    };
  }
}
