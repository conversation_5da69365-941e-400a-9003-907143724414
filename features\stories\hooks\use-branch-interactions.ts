import { useState, useEffect, useCallback } from 'react';
import {
  getBranchComments,
  addBranchComment,
  deleteBranchComment,
  getBranchVoteStats,
  getUserBranchVote,
  voteBranch,
  BranchComment,
  BranchVote,
  BranchVoteStats,
} from '@/api/stories';
import { useAuthStore } from '@/lib/store/auth-store';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';

interface UseBranchInteractionsProps {
  segmentId?: string;
}

export function useBranchInteractions({ segmentId }: UseBranchInteractionsProps) {
  const { t } = useTranslation();
  const { user } = useAuthStore();
  
  // 状态
  const [comments, setComments] = useState<BranchComment[]>([]);
  const [voteStats, setVoteStats] = useState<BranchVoteStats | null>(null);
  const [userVote, setUserVote] = useState<BranchVote | null>(null);
  const [isLoadingComments, setIsLoadingComments] = useState(false);
  const [isLoadingVotes, setIsLoadingVotes] = useState(false);
  const [isAddingComment, setIsAddingComment] = useState(false);
  const [isDeletingComment, setIsDeletingComment] = useState(false);
  const [isVoting, setIsVoting] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // 获取分支评论
  const fetchComments = useCallback(async () => {
    if (!segmentId) return;
    
    setIsLoadingComments(true);
    setError(null);
    
    try {
      const { data, error } = await getBranchComments(segmentId);
      
      if (error) {
        throw error;
      }
      
      setComments(data || []);
    } catch (err) {
      console.error('Error fetching branch comments:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch branch comments'));
    } finally {
      setIsLoadingComments(false);
    }
  }, [segmentId]);

  // 获取分支投票统计
  const fetchVoteStats = useCallback(async () => {
    if (!segmentId) return;
    
    setIsLoadingVotes(true);
    
    try {
      const { data, error } = await getBranchVoteStats(segmentId);
      
      if (error) {
        throw error;
      }
      
      setVoteStats(data);
    } catch (err) {
      console.error('Error fetching branch vote stats:', err);
      // 不设置全局错误，因为这只是投票统计获取失败
    } finally {
      setIsLoadingVotes(false);
    }
  }, [segmentId]);

  // 获取用户投票
  const fetchUserVote = useCallback(async () => {
    if (!segmentId || !user) return;
    
    try {
      const { data, error } = await getUserBranchVote(segmentId);
      
      if (error) {
        throw error;
      }
      
      setUserVote(data);
    } catch (err) {
      console.error('Error fetching user branch vote:', err);
      // 不设置全局错误，因为这只是用户投票获取失败
    }
  }, [segmentId, user]);

  // 添加评论
  const handleAddComment = useCallback(async (content: string) => {
    if (!segmentId || !user) {
      Alert.alert(
        t('error', 'Error'),
        t('storyDetail.errors.notLoggedInComment', 'You must be logged in to add a comment.')
      );
      return null;
    }
    
    if (!content.trim()) {
      Alert.alert(
        t('error', 'Error'),
        t('storyDetail.errors.emptyComment', 'Cannot add an empty comment.')
      );
      return null;
    }
    
    setIsAddingComment(true);
    
    try {
      const { data, error } = await addBranchComment(segmentId, content.trim());
      
      if (error) {
        throw error;
      }
      
      // 添加新评论到列表
      if (data) {
        setComments(prev => [...prev, data]);
      }
      
      return data;
    } catch (err) {
      console.error('Error adding comment:', err);
      Alert.alert(
        t('error', 'Error'),
        t('storyDetail.errors.addCommentFailed', 'Failed to add comment.')
      );
      return null;
    } finally {
      setIsAddingComment(false);
    }
  }, [segmentId, user, t]);

  // 删除评论
  const handleDeleteComment = useCallback(async (commentId: string) => {
    if (!user) {
      Alert.alert(
        t('error', 'Error'),
        t('storyDetail.errors.notLoggedInComment', 'You must be logged in to delete a comment.')
      );
      return false;
    }
    
    setIsDeletingComment(true);
    
    try {
      const { success, error } = await deleteBranchComment(commentId);
      
      if (error) {
        throw error;
      }
      
      if (success) {
        // 从列表中移除评论
        setComments(prev => prev.filter(comment => comment.id !== commentId));
        return true;
      }
      
      return false;
    } catch (err) {
      console.error('Error deleting comment:', err);
      Alert.alert(
        t('error', 'Error'),
        t('storyDetail.errors.deleteCommentFailed', 'Failed to delete comment.')
      );
      return false;
    } finally {
      setIsDeletingComment(false);
    }
  }, [user, t]);

  // 投票
  const handleVote = useCallback(async (voteType: 'up' | 'down') => {
    if (!segmentId || !user) {
      Alert.alert(
        t('error', 'Error'),
        t('storyDetail.errors.notLoggedInVote', 'You must be logged in to vote.')
      );
      return false;
    }
    
    setIsVoting(true);
    
    try {
      const { data, error } = await voteBranch(segmentId, voteType);
      
      if (error) {
        throw error;
      }
      
      // 更新用户投票和投票统计
      setUserVote(data);
      await fetchVoteStats();
      
      return true;
    } catch (err) {
      console.error('Error voting:', err);
      Alert.alert(
        t('error', 'Error'),
        t('storyDetail.errors.voteFailed', 'Failed to vote.')
      );
      return false;
    } finally {
      setIsVoting(false);
    }
  }, [segmentId, user, t, fetchVoteStats]);

  // 初始加载
  useEffect(() => {
    if (segmentId) {
      fetchComments();
      fetchVoteStats();
      fetchUserVote();
    }
  }, [segmentId, fetchComments, fetchVoteStats, fetchUserVote]);

  return {
    comments,
    voteStats,
    userVote,
    isLoadingComments,
    isLoadingVotes,
    isAddingComment,
    isDeletingComment,
    isVoting,
    error,
    addComment: handleAddComment,
    deleteComment: handleDeleteComment,
    vote: handleVote,
    refreshComments: fetchComments,
    refreshVotes: fetchVoteStats,
  };
}
