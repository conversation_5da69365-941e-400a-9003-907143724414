import { useSettingsStore } from '@/lib/store/settings-store';
import { useTranslation } from 'react-i18next';

/**
 * Hook to get the current locale for internationalization
 * Used for date formatting and other locale-specific functionality
 */
export function useLocale() {
  const { i18n } = useTranslation();
  const language = useSettingsStore((state) => state.language);
  
  // Get the current locale from i18n or settings store
  const locale = language || i18n.language || 'en';
  
  return { locale };
}