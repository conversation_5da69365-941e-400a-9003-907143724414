import React from 'react';
import { ScrollView } from '@/components/ui/scroll-view';
import { Pressable } from '@/components/ui/pressable';
import { Text } from '@/components/ui/text';
import { useColorScheme } from 'nativewind';

export interface FilterOption {
  id: string;
  label: string;
}

interface FilterChipsProps {
  options: FilterOption[];
  selectedIds: string[];
  onSelect: (id: string) => void;
  multiSelect?: boolean;
}

export default function FilterChips({
  options,
  selectedIds,
  onSelect,
  multiSelect = false,
}: FilterChipsProps) {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const handleSelect = (id: string) => {
    onSelect(id);
  };

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      className="px-4 py-2"
    >
      {options.map((option) => {
        const isSelected = selectedIds.includes(option.id);
        return (
          <Pressable
            key={option.id}
            className={`px-4 py-2 rounded-full mr-2 border ${
              isSelected
                ? 'bg-primary-500 border-primary-500'
                : isDark
                ? 'bg-background-800 border-outline-700'
                : 'bg-background-100 border-outline-200'
            }`}
            onPress={() => handleSelect(option.id)}
          >
            <Text
              className={`text-sm font-medium ${
                isSelected
                  ? 'text-white'
                  : isDark
                  ? 'text-typography-50'
                  : 'text-typography-900'
              }`}
            >
              {option.label}
            </Text>
          </Pressable>
        );
      })}
    </ScrollView>
  );
}
