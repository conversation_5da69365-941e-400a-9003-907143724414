import React from 'react';

// Import gluestack-ui components
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { HStack } from '@/components/ui/hstack';

interface StoryListItemTagsProps {
  themes: string[] | undefined;
  isCompleted: boolean | undefined;
}

export function StoryListItemTags({
  themes,
  isCompleted,
}: StoryListItemTagsProps) {
  return (
    <HStack className="flex-wrap gap-1 items-center">
      {themes?.map((themeName) => (
        <Box
          key={themeName}
          className="px-1 py-0.5 rounded-sm bg-primary-100 dark:bg-primary-900"
        >
          <Text className="text-xs text-primary-500 dark:text-primary-400">
            {themeName}
          </Text>
        </Box>
      ))}

      {typeof isCompleted === 'boolean' &&
        (isCompleted ? (
          <Box className="px-1 py-0.5 rounded-sm bg-success-100 dark:bg-success-900">
            <Text className="text-xs font-medium text-success-500 dark:text-success-400">
              已完结
            </Text>
          </Box>
        ) : (
          <Box className="px-1 py-0.5 rounded-sm bg-warning-100 dark:bg-warning-900">
            <Text className="text-xs font-medium text-warning-500 dark:text-warning-400">
              连载中
            </Text>
          </Box>
        ))}
    </HStack>
  );
}
