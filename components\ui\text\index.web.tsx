import React from 'react';
import type { VariantProps } from '@gluestack-ui/nativewind-utils';
import { textStyle } from './styles';

type ITextProps = React.ComponentProps<'span'> & VariantProps<typeof textStyle>;

const Text = React.forwardRef<React.ComponentRef<'span'>, ITextProps>(
  function Text(
    {
      className,
      isTruncated,
      bold,
      underline,
      strikeThrough,
      size = 'md',
      sub,
      italic,
      highlight,
      // Filter out React Native specific props for web
      numberOfLines,
      ellipsizeMode,
      onLayout,
      ...props
    }: {
      className?: string;
      numberOfLines?: number;
      ellipsizeMode?: string;
      onLayout?: any;
    } & ITextProps,
    ref
  ) {
    // If numberOfLines is set, we can simulate truncation with CSS
    const truncateClass = numberOfLines ? 'truncate' : '';

    return (
      <span
        className={textStyle({
          isTruncated: isTruncated || !!numberOfLines,
          bold,
          underline,
          strikeThrough,
          size,
          sub,
          italic,
          highlight,
          class: `${className || ''} ${truncateClass}`,
        })}
        {...props}
        ref={ref}
      />
    );
  }
);

Text.displayName = 'Text';

export { Text };
