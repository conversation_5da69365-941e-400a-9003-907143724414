import React from 'react';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Button, ButtonText } from '@/components/ui/m3e-button';
import { useTranslation } from 'react-i18next';

interface HomeScreenErrorProps {
  error: string;
  onRetry: () => void;
}

export function HomeScreenError({ error, onRetry }: HomeScreenErrorProps) {
  const { t } = useTranslation();

  return (
    <Box className="flex-1 justify-center items-center mt-5">
      <Text className="mb-4 text-center text-error-600 dark:text-error-400">
        {error}
      </Text>
      <Button
        action="primary"
        variant="solid"
        size="md"
        onPress={onRetry}
        className="bg-primary-500 dark:bg-primary-600 rounded-md"
      >
        <ButtonText className="text-typography-950 dark:text-typography-50">
          {t('tryAgain', '重试')}
        </ButtonText>
      </Button>
    </Box>
  );
}
