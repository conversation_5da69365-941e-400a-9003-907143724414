import { supabase } from '@/utils/supabase';
import { PostgrestError } from '@supabase/supabase-js';
import { Story } from './types';

/**
 * Updates the details of a story
 */
export async function updateStoryDetails(
  storyId: string,
  updates: Partial<
    Pick<Story, 'title' | 'status' | 'visibility' | 'tags' | 'cover_image_url'>
  >
): Promise<{ data: Story | null; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user)
    return {
      data: null,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };

  // Ensure only the author can update (R<PERSON> should also enforce this)
  // const { data: existingStory, error: fetchError } = await supabase.from('stories').select('author_id').eq('id', storyId).single();
  // if (fetchError || !existingStory || existingStory.author_id !== user.id) {
  //   return { data: null, error: fetchError || { message: 'Forbidden or story not found', ... } as PostgrestError };
  // }

  const { data, error } = await supabase
    .from('stories')
    .update(updates)
    .eq('id', storyId)
    // .eq('author_id', user.id) // Double check with RLS, but good for client-side guard
    .select(
      `
        id, author_id, title, status, visibility, tags, cover_image_url, created_at, updated_at,
        profiles!stories_author_id_fkey ( id, username, avatar_url )
    `
    )
    .single();

  if (error) console.error('Error updating story:', error);
  // @ts-ignore
  return { data, error };
}
