import React, { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/m3e-button';
import { ButtonText } from '@/components/ui/m3e-button';
import { ButtonIcon } from '@/components/ui/m3e-button';
import { Center } from '@/components/ui/center';
import { VStack } from '@/components/ui/vstack';

interface PlaceholderTabProps {
  icon: ReactNode;
  title: string;
  subtitle: string;
  actionLabel?: string;
  onAction?: () => void;
  actionIcon?: ReactNode;
}

export function PlaceholderTab({
  icon,
  title,
  subtitle,
  actionLabel,
  onAction,
  actionIcon,
}: PlaceholderTabProps) {
  const { t } = useTranslation();

  return (
    <Center className="flex-1 px-8 bg-background-50 dark:bg-background-900">
      <VStack className="items-center">
        <Box className="mb-6">
          {icon}
        </Box>
        
        <Text className="text-xl font-bold text-typography-900 dark:text-typography-100 text-center mb-2">
          {title}
        </Text>
        
        <Text className="text-base text-typography-500 dark:text-typography-400 text-center mb-8">
          {subtitle}
        </Text>
        
        {actionLabel && onAction && (
          <Button
            size="lg"
            variant="solid"
            action="primary"
            onPress={onAction}
            className="px-6 py-3 rounded-lg bg-primary-500 dark:bg-primary-600 flex-row items-center"
          >
            {actionIcon && (
              <ButtonIcon className="mr-2">
                {actionIcon}
              </ButtonIcon>
            )}
            <ButtonText className="text-typography-950 dark:text-typography-50 font-bold">
              {actionLabel}
            </ButtonText>
          </Button>
        )}
      </VStack>
    </Center>
  );
}
