import React from 'react';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Spinner } from '@/components/ui/spinner';
import { useTranslation } from 'react-i18next';
import { useColorScheme } from 'nativewind';

interface ListFooterProps {
  isLoading: boolean;
}

export function ListFooter({ isLoading }: ListFooterProps) {
  const { t } = useTranslation();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  if (!isLoading) return null;

  return (
    <HStack className="justify-center items-center py-4 space-x-2">
      <Spinner size="small" color={isDark ? 'primary.400' : 'primary.500'} />
      <Text
        className={`text-sm ${
          isDark ? 'text-typography-400' : 'text-typography-500'
        }`}
      >
        {t('social.feed.loadingMore', '加载更多...')}
      </Text>
    </HStack>
  );
}
