import { supabase } from '@/utils/supabase';
import { AuthError, User, Session } from '@supabase/supabase-js';
import * as Linking from 'expo-linking';

// Handles user sign-up with email and password, and username metadata
export async function signUpWithEmail(
  email: string,
  password: string,
  username: string
): Promise<{
  data: { user: User | null; session: Session | null } | null;
  error: AuthError | null;
}> {
  const { data, error } = await supabase.auth.signUp({
    email: email,
    password: password,
    options: {
      data: {
        username: username,
        display_name: username,
      },
    },
  });
  return { data: { user: data.user, session: data.session }, error };
}

// Handles user sign-in with email and password
export async function signInWithEmail(
  email: string,
  password: string
): Promise<{
  data: { user: User | null; session: Session | null } | null;
  error: AuthError | null;
}> {
  const { data, error } = await supabase.auth.signInWithPassword({
    email: email,
    password: password,
  });
  return { data: { user: data.user, session: data.session }, error };
}

// Gets the current authenticated user
export async function getCurrentUser(): Promise<{
  data: { user: User | null; session: Session | null } | null;
  error: AuthError | null;
}> {
  const {
    data: { session },
    error: sessionError,
  } = await supabase.auth.getSession();
  if (sessionError) {
    return { data: null, error: sessionError };
  }
  if (session) {
    return { data: { user: session.user, session: session }, error: null };
  }
  return { data: null, error: null };
}

// Handles user sign-out
export async function signOut(): Promise<{ error: AuthError | null }> {
  const { error } = await supabase.auth.signOut();
  return { error };
}

/**
 * Sends a password reset email to the specified email address
 * @param email The email address to send the password reset link to
 * @returns An object containing error information if the operation failed
 */
export async function resetPassword(
  email: string
): Promise<{ error: AuthError | null }> {
  // Get the deep link URL for the app
  const redirectUrl = Linking.createURL('reset-password');

  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: redirectUrl,
  });
  return { error };
}

/**
 * Updates the user's password after they've received a reset password email
 * @param newPassword The new password to set
 * @returns An object containing session and user if successful, or error if failed
 */
export async function updatePassword(newPassword: string): Promise<{
  data: { user: User | null } | null;
  error: AuthError | null;
}> {
  const { data, error } = await supabase.auth.updateUser({
    password: newPassword,
  });

  return {
    data: data ? { user: data.user } : null,
    error,
  };
}
