import React from 'react';
import { Pressable } from '@/components/ui/pressable';
import { Text } from '@/components/ui/text';
import { HStack } from '@/components/ui/hstack';
import {
  BookOpen,
  Rocket,
  Wand,
  Landmark,
  Heart,
  Search,
  Building,
} from 'lucide-react-native';
import { useColorScheme } from 'nativewind';

// Interface for the theme data passed as prop
export interface Theme {
  id: string;
  name: string;
  icon?: string; // Added icon based on switch usage
  color?: string; // Optional specific background color
}

interface ThemeCardProps {
  theme: Theme; // Use the defined interface
  onPress?: (themeId: string) => void;
  className?: string; // Optional className for additional styling
}

export default function ThemeCard({
  theme: themeProp,
  onPress,
  className = '',
}: ThemeCardProps) {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const renderIcon = () => {
    // Use white for icon color for better contrast on colored backgrounds
    const iconColor = '#FFFFFF';
    const size = 18;

    // Use themeProp for icon logic
    switch (themeProp.icon) {
      case 'rocket':
        return <Rocket size={size} color={iconColor} />;
      case 'wand':
        return <Wand size={size} color={iconColor} />;
      case 'search':
        return <Search size={size} color={iconColor} />;
      case 'heart':
        return <Heart size={size} color={iconColor} />;
      case 'landmark':
        return <Landmark size={size} color={iconColor} />;
      case 'building':
        return <Building size={size} color={iconColor} />;
      default:
        return <BookOpen size={size} color={iconColor} />;
    }
  };

  // Use inline style for dynamic background color based on theme
  const dynamicStyle = {
    backgroundColor: themeProp.color || '#6366F1', // primary-500 as fallback
  };

  return (
    <Pressable
      className={`px-4 py-2 rounded-md flex-row items-center min-w-[80px] mr-2 mb-2 ${className}`}
      style={dynamicStyle}
      onPress={() => onPress?.(themeProp.id)}
    >
      {renderIcon()}
      <Text className="text-white font-medium text-sm ml-1">
        {themeProp.name}
      </Text>
    </Pressable>
  );
}
