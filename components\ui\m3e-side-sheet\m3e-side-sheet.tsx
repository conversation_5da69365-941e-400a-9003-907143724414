import React, { useRef, useEffect } from 'react';
import { View, Text, Modal, TouchableOpacity, Animated, Dimensions } from 'react-native';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

// Side Sheet Action 的属性接口
export interface M3ESideSheetAction {
  /** 标签文本 */
  label: string;
  /** 是否为主要按钮 */
  primary?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 点击事件 */
  onPress?: () => void;
}

// Side Sheet 的属性接口
export interface M3ESideSheetProps {
  /** 是否显示 */
  visible: boolean;
  /** 类型：模态或标准 */
  type?: 'modal' | 'standard';
  /** 标题 */
  title?: string;
  /** 是否显示返回按钮 */
  showBackButton?: boolean;
  /** 是否显示关闭按钮 */
  showCloseButton?: boolean;
  /** 是否显示操作按钮 */
  showActions?: boolean;
  /** 操作按钮列表 */
  actions?: M3ESideSheetAction[];
  /** 子组件 */
  children?: React.ReactNode;
  /** 宽度（像素） */
  width?: number;
  /** 从右侧滑入 */
  slideFromRight?: boolean;
  /** 返回事件 */
  onBack?: () => void;
  /** 关闭事件 */
  onClose?: () => void;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * M3E Side Sheet 组件
 *
 * 基于 Material Design 3 规范的侧边表单组件，包含补充内容或操作，
 * 通常锚定在大屏幕（如平板和桌面）的右边缘。
 *
 * @example
 * ```tsx
 * const actions = [
 *   { label: 'Cancel', onPress: () => console.log('Cancel') },
 *   { label: 'Save', primary: true, onPress: () => console.log('Save') },
 * ];
 * 
 * <M3ESideSheet
 *   visible={showSideSheet}
 *   type="modal"
 *   title="Side Sheet Title"
 *   showBackButton={true}
 *   showActions={true}
 *   actions={actions}
 *   onBack={() => console.log('Back')}
 *   onClose={() => setShowSideSheet(false)}
 * >
 *   <View className="p-6">
 *     <Text>Side sheet content goes here</Text>
 *   </View>
 * </M3ESideSheet>
 * ```
 */
export const M3ESideSheet: React.FC<M3ESideSheetProps> = ({
  visible,
  type = 'modal',
  title,
  showBackButton = false,
  showCloseButton = true,
  showActions = false,
  actions = [],
  children,
  width = 320,
  slideFromRight = true,
  onBack,
  onClose,
  className = '',
}) => {
  const translateX = useRef(new Animated.Value(slideFromRight ? width : -width)).current;

  useEffect(() => {
    if (visible) {
      // 显示动画
      Animated.spring(translateX, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    } else {
      // 隐藏动画
      Animated.spring(translateX, {
        toValue: slideFromRight ? width : -width,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    }
  }, [visible, width, slideFromRight, translateX]);

  const handleBackdropPress = () => {
    if (type === 'modal' && onClose) {
      onClose();
    }
  };

  const getContainerClasses = () => {
    let classes = 'bg-purple-50 dark:bg-purple-900';
    
    if (type === 'modal') {
      classes += ' shadow-lg rounded-2xl';
    } else {
      classes += ' shadow-md border-l border-gray-200 dark:border-gray-700';
    }
    
    return `${classes} ${className}`;
  };

  const renderActions = () => {
    if (!showActions || actions.length === 0) {
      return null;
    }

    return (
      <View className="border-t border-gray-200 dark:border-gray-700">
        <View className="flex-row items-center gap-2 px-6 py-3">
          {actions.map((action, index) => (
            <TouchableOpacity
              key={index}
              onPress={action.onPress}
              disabled={action.disabled}
              className={`
                px-4 py-2 rounded-full
                ${action.primary 
                  ? 'bg-purple-600 dark:bg-purple-500' 
                  : 'border border-gray-300 dark:border-gray-600'
                }
                ${action.disabled ? 'opacity-50' : ''}
              `}
            >
              <Text
                className={`
                  text-sm font-medium
                  ${action.primary 
                    ? 'text-white' 
                    : 'text-gray-900 dark:text-white'
                  }
                `}
              >
                {action.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  const renderContent = () => (
    <Animated.View
      style={{
        transform: [{ translateX }],
        width,
      }}
      className={`h-full ${getContainerClasses()}`}
    >
      {/* Header */}
      <View className="flex-row items-center px-3 py-3 border-b border-gray-200 dark:border-gray-700">
        {/* Back Button */}
        {showBackButton && (
          <TouchableOpacity
            onPress={onBack}
            className="w-12 h-12 items-center justify-center mr-2"
          >
            <Text className="text-gray-600 dark:text-gray-400 text-lg">←</Text>
          </TouchableOpacity>
        )}
        
        {/* Title */}
        <View className="flex-1 px-3">
          {title && (
            <Text className="text-xl font-normal text-gray-900 dark:text-white">
              {title}
            </Text>
          )}
        </View>
        
        {/* Close Button */}
        {showCloseButton && (
          <TouchableOpacity
            onPress={onClose}
            className="w-12 h-12 items-center justify-center"
          >
            <View className="w-6 h-6 bg-gray-400 rounded-full items-center justify-center">
              <Text className="text-white text-xs">×</Text>
            </View>
          </TouchableOpacity>
        )}
      </View>
      
      {/* Content */}
      <View className="flex-1">
        {children}
      </View>
      
      {/* Actions */}
      {renderActions()}
      
      {/* Vertical Divider for Standard Type */}
      {type === 'standard' && (
        <View className="absolute left-0 top-0 bottom-0 w-px bg-gray-200 dark:bg-gray-700" />
      )}
    </Animated.View>
  );

  if (!visible) {
    return null;
  }

  if (type === 'modal') {
    return (
      <Modal
        visible={visible}
        transparent
        animationType="none"
        onRequestClose={onClose}
      >
        <View className="flex-1 flex-row">
          {/* Backdrop/Scrim */}
          <TouchableOpacity
            className="flex-1 bg-black opacity-30"
            activeOpacity={1}
            onPress={handleBackdropPress}
          />
          
          {/* Side Sheet */}
          <View className={slideFromRight ? 'justify-center' : 'justify-center'}>
            {renderContent()}
          </View>
        </View>
      </Modal>
    );
  }

  return (
    <View className={`absolute top-0 bottom-0 z-50 ${slideFromRight ? 'right-0' : 'left-0'}`}>
      {renderContent()}
    </View>
  );
};

export default M3ESideSheet;
