import React from 'react';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Eye, Heart } from 'lucide-react-native';

interface FeaturedStoryCardStatsProps {
  authorName: string;
  views: number;
  likes: number;
}

export function FeaturedStoryCardStats({
  authorName,
  views,
  likes,
}: FeaturedStoryCardStatsProps) {
  return (
    <HStack className="justify-between items-center mb-3">
      <Text className="text-sm font-medium text-white">@{authorName}</Text>

      <HStack className="space-x-3">
        <HStack className="items-center space-x-1">
          <Eye size={12} color="#FFFFFF" />
          <Text className="text-xs text-white">{views}</Text>
        </HStack>

        <HStack className="items-center space-x-1">
          <Heart size={12} color="#FFFFFF" fill="#FFFFFF" />
          <Text className="text-xs text-white">{likes}</Text>
        </HStack>
      </HStack>
    </HStack>
  );
}
