import React from 'react';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Textarea, TextareaInput } from '@/components/ui/textarea';
import { HStack } from '@/components/ui/hstack';
import { BookOpen } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';

interface ContentInputProps {
  content: string;
  onContentChange: (text: string) => void;
  isFocused: boolean;
  onFocus: () => void;
  onBlur: () => void;
}

export default function ContentInput({
  content,
  onContentChange,
  isFocused,
  onFocus,
  onBlur,
}: ContentInputProps) {
  const { t } = useTranslation();

  return (
    <Box className="mb-6">
      <HStack className="items-center mb-2">
        <BookOpen size={18} className="text-primary-500" />
        <Text
          size="md"
          className="font-medium text-typography-900 dark:text-typography-50 ml-2"
        >
          {t('storyForm.initialContentLabel', '开始你的故事')}
        </Text>
      </HStack>
      <Textarea
        size="md"
        variant="outline"
        className={`min-h-[150px] ${
          isFocused
            ? 'border-2 border-primary-500'
            : content.length > 0
            ? 'border border-primary-500'
            : 'border border-background-300 dark:border-background-700'
        }`}
      >
        <TextareaInput
          value={content}
          onChangeText={onContentChange}
          placeholder={t('storyForm.initialContentPlaceholder', '从前...')}
          multiline
          onFocus={onFocus}
          onBlur={onBlur}
          textAlignVertical="top"
        />
      </Textarea>
      {content.length > 0 && (
        <HStack className="justify-end items-center mt-1">
          <Text
            size="xs"
            className={`${
              content.length < 50 ? 'text-warning-500' : 'text-success-500'
            }`}
          >
            {content.length < 50
              ? `${t('storyForm.contentMinLength', '至少需要50个字符')} (${
                  content.length
                }/50)`
              : `${content.length} ${t('storyForm.characters', '个字符')}`}
          </Text>
        </HStack>
      )}
    </Box>
  );
}
