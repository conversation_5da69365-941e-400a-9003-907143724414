import React from 'react';
import { useTranslation } from 'react-i18next';
import { Crown, Star } from 'lucide-react-native';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Image } from '@/components/ui/image';
import { Center } from '@/components/ui/center/index';

interface ProfileHeaderProps {
  name: string;
  bio: string;
  avatarUrl?: string;
  isPremium?: boolean;
}

export function ProfileHeader({
  name,
  bio,
  avatarUrl,
  isPremium,
}: ProfileHeaderProps) {
  const { t } = useTranslation();

  return (
    <Center className="mb-6 px-4 pt-6 bg-background-950">
      <Box className="mb-4 relative">
        <Center className="w-[120px] h-[120px] bg-background-900 rounded-full overflow-hidden border-2 border-primary-500">
          {avatarUrl ? (
            <Image
              source={{ uri: avatarUrl }}
              alt={name}
              className="w-full h-full"
              resizeMode="cover"
            />
          ) : (
            <Star size={56} color="#FFFFFF" />
          )}
        </Center>
        {isPremium && (
          <Box className="absolute top-2 right-2 bg-yellow-400 flex-row items-center px-2 py-0.5 rounded-full shadow">
            <Crown size={12} color="#000" />
            <Text className="text-xs font-bold text-black ml-0.5">
              {t('premium')}
            </Text>
          </Box>
        )}
      </Box>
      <Text className="text-2xl font-bold mb-2 text-typography-50">{name}</Text>
      <Text className="text-sm text-typography-400 text-center mb-2 max-w-xs">
        {bio}
      </Text>
    </Center>
  );
}
