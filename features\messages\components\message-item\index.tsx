import React from 'react';
import { Message } from '@/api/messages/types';
import { formatDistanceToNow } from 'date-fns';
import { zhCN, enUS } from 'date-fns/locale';
import { useTranslation } from 'react-i18next';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Image } from '@/components/ui/image';

interface MessageItemProps {
  message: Message;
  isCurrentUser: boolean;
}

export default function MessageItem({
  message,
  isCurrentUser,
}: MessageItemProps) {
  const { t, i18n } = useTranslation();

  // 格式化时间
  const formatTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, {
        addSuffix: true,
        locale: i18n.language === 'zh' ? zhCN : enUS,
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  return (
    <Box
      className={`flex-row my-2 px-4 ${
        isCurrentUser ? 'justify-end' : 'justify-start'
      }`}
    >
      {!isCurrentUser && message.sender && (
        <Box className="mx-2">
          {message.sender.avatar_url ? (
            <Image
              source={{ uri: message.sender.avatar_url }}
              className="w-9 h-9 rounded-full"
              alt={message.sender.username || 'User avatar'}
            />
          ) : (
            <Box className="w-9 h-9 rounded-full bg-background-300" />
          )}
        </Box>
      )}

      <Box
        className={`max-w-[70%] rounded-md p-4 ${
          isCurrentUser
            ? 'bg-primary-500 rounded-br-none'
            : 'bg-background-100 rounded-bl-none'
        }`}
      >
        <Text className="text-base text-typography-900">{message.content}</Text>
        <Text className="text-xs text-typography-500 mt-1 self-end">
          {formatTime(message.created_at)}
        </Text>
      </Box>

      {isCurrentUser && (
        <Box className="mx-2">
          {message.sender && message.sender.avatar_url ? (
            <Image
              source={{ uri: message.sender.avatar_url }}
              className="w-9 h-9 rounded-full"
              alt={message.sender.username || 'User avatar'}
            />
          ) : (
            <Box className="w-9 h-9 rounded-full bg-background-300" />
          )}
        </Box>
      )}
    </Box>
  );
}
