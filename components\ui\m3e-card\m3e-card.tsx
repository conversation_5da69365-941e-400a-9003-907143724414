import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  ImageSourcePropType,
} from 'react-native';

// Card Action 的属性接口
export interface M3ECardActionProps {
  /** 按钮文本 */
  label: string;
  /** 按钮类型 */
  variant?: 'filled' | 'outlined' | 'text';
  /** 点击事件 */
  onPress?: () => void;
  /** 是否禁用 */
  disabled?: boolean;
}

// Card Header 的属性接口
export interface M3ECardHeaderProps {
  /** 头像组件或图片源 */
  avatar?: React.ReactNode | ImageSourcePropType;
  /** 头部标题 */
  title?: string;
  /** 头部副标题 */
  subtitle?: string;
  /** 右侧操作按钮 */
  action?: React.ReactNode;
}

// Card 的属性接口
export interface M3ECardProps {
  /** 卡片样式 */
  variant?: 'elevated' | 'filled' | 'outlined';
  /** 卡片头部 */
  header?: M3ECardHeaderProps;
  /** 媒体内容 */
  media?: {
    source: ImageSourcePropType;
    aspectRatio?: number;
    height?: number;
  };
  /** 卡片标题 */
  title?: string;
  /** 卡片副标题 */
  subtitle?: string;
  /** 支持文本 */
  supportingText?: string;
  /** 主要操作按钮 */
  primaryAction?: M3ECardActionProps;
  /** 次要操作按钮 */
  secondaryAction?: M3ECardActionProps;
  /** 自定义内容 */
  children?: React.ReactNode;
  /** 点击事件 */
  onPress?: () => void;
  /** 自定义样式类名 */
  className?: string;
}

// 获取卡片容器样式
const getCardContainerClasses = (variant: string, pressable: boolean) => {
  const variantClasses = {
    elevated: 'bg-white dark:bg-gray-800 shadow-md',
    filled: 'bg-gray-50 dark:bg-gray-900',
    outlined:
      'bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600',
  };

  const pressableClass = pressable ? 'active:opacity-80' : '';

  return `${
    variantClasses[variant as keyof typeof variantClasses] ||
    variantClasses.elevated
  } ${pressableClass}`;
};

// 获取操作按钮样式
const getActionButtonClasses = (variant: string, disabled: boolean) => {
  const variantClasses = {
    filled: 'bg-purple-600 rounded-full px-4 py-2.5',
    outlined:
      'border border-gray-300 dark:border-gray-600 rounded-full px-4 py-2.5',
    text: 'px-4 py-2.5',
  };

  const disabledClass = disabled ? 'opacity-50' : '';

  return `${
    variantClasses[variant as keyof typeof variantClasses] ||
    variantClasses.filled
  } ${disabledClass}`;
};

/**
 * Card Action 组件
 */
const CardAction: React.FC<M3ECardActionProps> = ({
  label,
  variant = 'filled',
  onPress,
  disabled = false,
}) => {
  const textColorClass =
    variant === 'filled'
      ? 'text-white'
      : 'text-purple-600 dark:text-purple-400';

  return (
    <TouchableOpacity
      onPress={disabled ? undefined : onPress}
      activeOpacity={0.7}
      className={`h-10 ${getActionButtonClasses(variant, disabled)}`}
    >
      <Text className={`text-sm font-medium ${textColorClass}`}>{label}</Text>
    </TouchableOpacity>
  );
};

/**
 * Card Header 组件
 */
const CardHeader: React.FC<M3ECardHeaderProps> = ({
  avatar,
  title,
  subtitle,
  action,
}) => {
  return (
    <View className="flex-row items-center px-4 py-3">
      <View className="flex-row items-center flex-1 gap-4">
        {avatar && (
          <View className="w-10 h-10 rounded-full overflow-hidden bg-purple-100 dark:bg-purple-900 items-center justify-center">
            {React.isValidElement(avatar) ? (
              avatar
            ) : (
              <Image
                source={avatar as ImageSourcePropType}
                className="w-full h-full"
                resizeMode="cover"
              />
            )}
          </View>
        )}

        {(title || subtitle) && (
          <View className="flex-1">
            {title && (
              <Text className="text-base font-medium text-gray-900 dark:text-white">
                {title}
              </Text>
            )}
            {subtitle && (
              <Text className="text-sm text-gray-600 dark:text-gray-400">
                {subtitle}
              </Text>
            )}
          </View>
        )}
      </View>

      {action && <View>{action}</View>}
    </View>
  );
};

/**
 * M3E Card 组件
 *
 * 基于 Material Design 3 规范的卡片组件，用于显示内容和操作。
 *
 * @example
 * ```tsx
 * <M3ECard
 *   variant="elevated"
 *   header={{
 *     avatar: <Icon name="person" />,
 *     title: "用户名",
 *     subtitle: "副标题",
 *     action: <IconButton icon="more-vert" />
 *   }}
 *   media={{
 *     source: { uri: 'https://example.com/image.jpg' },
 *     height: 200
 *   }}
 *   title="卡片标题"
 *   supportingText="这是支持文本，提供更多信息..."
 *   primaryAction={{
 *     label: "主要操作",
 *     onPress: () => console.log('Primary action')
 *   }}
 *   secondaryAction={{
 *     label: "次要操作",
 *     variant: "outlined",
 *     onPress: () => console.log('Secondary action')
 *   }}
 * />
 * ```
 */
export const M3ECard: React.FC<M3ECardProps> = ({
  variant = 'elevated',
  header,
  media,
  title,
  subtitle,
  supportingText,
  primaryAction,
  secondaryAction,
  children,
  onPress,
  className = '',
}) => {
  const baseClasses = 'rounded-xl overflow-hidden';
  const combinedClasses = `${baseClasses} ${className}`;

  const isPressable = !!onPress;

  return (
    <TouchableOpacity
      onPress={onPress}
      activeOpacity={isPressable ? 0.8 : 1}
      className={`${combinedClasses} ${getCardContainerClasses(
        variant,
        isPressable
      )}`}
    >
      {/* Header */}
      {header && <CardHeader {...header} />}

      {/* Media */}
      {media && (
        <View className="w-full" style={{ height: media.height || 200 }}>
          <Image
            source={media.source}
            className="w-full h-full"
            resizeMode="cover"
            style={{ aspectRatio: media.aspectRatio }}
          />
        </View>
      )}

      {/* Content */}
      {(title || subtitle || supportingText || children) && (
        <View className="px-4 py-4 gap-2">
          {title && (
            <Text className="text-lg font-normal text-gray-900 dark:text-white">
              {title}
            </Text>
          )}

          {subtitle && (
            <Text className="text-sm text-gray-600 dark:text-gray-400">
              {subtitle}
            </Text>
          )}

          {supportingText && (
            <Text className="text-sm text-gray-600 dark:text-gray-400 leading-5">
              {supportingText}
            </Text>
          )}

          {children}
        </View>
      )}

      {/* Actions */}
      {(primaryAction || secondaryAction) && (
        <View className="flex-row justify-end items-center gap-2 px-4 pb-4">
          {secondaryAction && <CardAction {...secondaryAction} />}
          {primaryAction && <CardAction {...primaryAction} />}
        </View>
      )}
    </TouchableOpacity>
  );
};

export default M3ECard;
