import React from 'react';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';
import { Crown } from 'lucide-react-native';
import { useTranslation } from 'react-i18next';
import { useColorScheme } from 'nativewind';

interface MembershipCardProps {
  isPremium: boolean;
  onManagePress?: () => void;
}

export function MembershipCard({
  isPremium,
  onManagePress,
}: MembershipCardProps) {
  const { t } = useTranslation();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Only render the card if the user is premium, or adjust logic as needed
  if (!isPremium) {
    return null; // Or render a different card prompting upgrade
  }

  return (
    <Pressable
      className="mx-4 mb-6 rounded-lg p-4 bg-primary-500 dark:bg-primary-600 shadow-sm"
      onPress={onManagePress}
      disabled={!onManagePress}
    >
      <HStack className="items-center">
        <Box className="mr-4 bg-white/20 p-2 rounded-full">
          <Crown size={28} color={isDark ? '#000' : '#FFF'} />
        </Box>
        <VStack className="flex-1">
          <Text className="text-lg font-bold text-white">
            {t('premiumMember')}
          </Text>
          <Text className="text-sm text-white/80">{t('premiumBenefits')}</Text>
        </VStack>
        {onManagePress && (
          <Text className="text-sm font-medium text-white underline">
            {t('manageMembership')}
          </Text>
        )}
      </HStack>
    </Pressable>
  );
}
