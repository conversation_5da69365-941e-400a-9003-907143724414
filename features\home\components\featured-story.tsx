import React from 'react';
import { Box } from '@/components/ui/box';
import FeaturedStoryCard from '@/components/stories/featured-story-card';
import { Story } from '@/api/stories'; // Import Story type directly from API

interface FeaturedStoryProps {
  story: Story; // Assuming Story type is exported from FeaturedStoryCard or a shared type file
  onPress?: (storyId: string) => void;
}

export function FeaturedStory({ story, onPress }: FeaturedStoryProps) {
  // Handle case where story might be null or undefined
  if (!story) {
    return null; 
  }

  return (
    <Box className="mb-6">
      <FeaturedStoryCard 
        story={story} 
        onPress={() => onPress?.(story.id)} 
      />
    </Box>
  );
}
