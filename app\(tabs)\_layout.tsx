import { Tabs, useRouter } from 'expo-router';
import {
  Book,
  PenLine,
  Compass,
  Users,
  User,
  Settings,
} from 'lucide-react-native';
import { TouchableOpacity } from 'react-native';
import TabBarIcon from '@/components/ui/tab-bar-icon';
import { useTheme } from '@/lib/theme/theme-provider';
import { useTranslation } from 'react-i18next';

export default function TabLayout() {
  const { colors, fonts, spacing } = useTheme();
  const router = useRouter();
  const { t } = useTranslation();

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.tabIconDefault,
        tabBarStyle: {
          backgroundColor: colors.background,
          borderTopColor: colors.border,
          elevation: 0,
          borderTopWidth: 1,
          height: 60,
          paddingBottom: spacing.xs,
          paddingTop: spacing.xs,
        },
        tabBarLabelStyle: {
          fontSize: 10,
          fontFamily: fonts.medium,
        },
        headerShown: false,
      }}
    >
      <Tabs.Screen
        name="home"
        options={{
          title: t('tabs.home'),
          tabBarIcon: ({ color }) => (
            <TabBarIcon>
              <Compass color={color} size={24} />
            </TabBarIcon>
          ),
        }}
      />
      <Tabs.Screen
        name="create"
        options={{
          title: t('tabs.create'),
          tabBarIcon: ({ color }) => (
            <TabBarIcon>
              <PenLine color={color} size={24} />
            </TabBarIcon>
          ),
        }}
      />
      <Tabs.Screen
        name="stories"
        options={{
          title: t('tabs.stories'),
          tabBarIcon: ({ color }) => (
            <TabBarIcon>
              <Book color={color} size={24} />
            </TabBarIcon>
          ),
        }}
      />
      <Tabs.Screen
        name="social"
        options={{
          title: t('tabs.social'),
          tabBarIcon: ({ color }) => (
            <TabBarIcon>
              <Users color={color} size={24} />
            </TabBarIcon>
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: t('tabs.profile'),
          headerShown: true,
          headerStyle: {
            backgroundColor: colors.background,
            boxShadow: 'none',
            borderBottomWidth: 1,
            borderBottomColor: colors.border,
          },
          headerTitleStyle: {
            color: colors.text,
            fontFamily: fonts.bold,
          },
          headerRight: () => (
            <TouchableOpacity
              onPress={() => router.push('/(settings)')}
              style={{ marginRight: spacing.md }}
            >
              <Settings size={24} color={colors.text} />
            </TouchableOpacity>
          ),
          tabBarIcon: ({ color }) => (
            <TabBarIcon>
              <User color={color} size={24} />
            </TabBarIcon>
          ),
        }}
      />
    </Tabs>
  );
}
