import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import * as Localization from 'expo-localization';

// Import individual translation files by domain
import en_common from '@/translations/en/common.json';
import en_auth from '@/translations/en/auth.json';
import en_home from '@/translations/en/home.json';
import en_profile from '@/translations/en/profile.json';
import en_stories from '@/translations/en/stories.json';
import en_social from '@/translations/en/social.json';
import en_settings from '@/translations/en/settings.json';
import en_rankings from '@/translations/en/rankings.json';

import zh_common from '@/translations/zh/common.json';
import zh_auth from '@/translations/zh/auth.json';
import zh_home from '@/translations/zh/home.json';
import zh_profile from '@/translations/zh/profile.json';
import zh_stories from '@/translations/zh/stories.json';
import zh_social from '@/translations/zh/social.json';
import zh_settings from '@/translations/zh/settings.json';
import zh_rankings from '@/translations/zh/rankings.json';

// Merge translation objects for each language
const enTranslations = {
  ...en_common,
  ...en_auth,
  ...en_home,
  ...en_profile,
  ...en_stories,
  ...en_social,
  ...en_settings,
  ...en_rankings,
};

const zhTranslations = {
  ...zh_common,
  ...zh_auth,
  ...zh_home,
  ...zh_profile,
  ...zh_stories,
  ...zh_social,
  ...zh_settings,
  ...zh_rankings,
};

const resources = {
  en: { translation: enTranslations },
  zh: { translation: zhTranslations },
};

const deviceLanguage = Localization.getLocales()[0]?.languageCode || 'en';

i18n
  .use(initReactI18next) // passes i18n down to react-i18next
  .init({
    resources,
    lng: deviceLanguage, // Expo Router 在服务器端可能无法很好地获取，客户端会覆盖
    fallbackLng: 'en', // use en if detected lng is not available
    compatibilityJSON: 'v3', // By default React Native projects does not support Intl
    interpolation: {
      escapeValue: false, // react already safes from xss
    },
  });

export default i18n;
