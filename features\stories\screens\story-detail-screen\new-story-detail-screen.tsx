import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { StorySegment } from '@/api/stories/types';
import { Box } from '@/components/ui/box';
import { SafeAreaView } from '@/components/ui/safe-area-view';

// Hooks
import { useStoryDetails } from '../../hooks/use-story-details';
import { useStoryFeed } from '../../hooks/use-story-feed';
import { useStorySegmentManagement } from '../../hooks/use-story-segment-management';

// Components
import StoryDetailHeader from '../../components/story-detail-header';
import StoryFeed from '../../components/story-feed';
import AddSegmentForm from '../../components/add-segment-form';
import BreadcrumbNavigation from '../../components/breadcrumb-navigation';
import { LoadingState } from './loading-state';
import { ErrorState } from './error-state';
import { EmptyState } from './empty-state';

interface StoryDetailScreenProps {
  storyId: string;
}

export default function NewStoryDetailScreen({
  storyId,
}: StoryDetailScreenProps) {
  const { t } = useTranslation();

  // Get story details
  const {
    story,
    isLoading: isLoadingDetails,
    error: storyError,
    isLiked,
    likeCount,
    isLiking,
    fetchStoryDetails,
    handleLikeToggle,
  } = useStoryDetails(storyId);

  // Get story feed with virtualization and pagination
  const {
    segments,
    isLoading: isLoadingSegments,
    error: segmentsError,
    hasMoreSegments,
    loadMore: loadMoreSegments,
    refresh: refreshSegments,
    navigateToBranch,
  } = useStoryFeed({
    storyId,
    pageSize: 10,
  });

  // Handle adding new segments
  const {
    newSegmentContent,
    setNewSegmentContent,
    isSubmittingSegment,
    handleAddSegment,
  } = useStorySegmentManagement({
    storyId,
    onSegmentAdded: (newSegment) => {
      refreshSegments();
    },
  });

  // Handle submitting a new segment
  const submitSegment = useCallback(async () => {
    if (!newSegmentContent.trim()) {
      return;
    }

    await handleAddSegment(newSegmentContent.trim());
    setNewSegmentContent('');
  }, [newSegmentContent, handleAddSegment]);

  // Handle branch selection
  const handleBranchSelect = useCallback(
    async (segmentId: string) => {
      await navigateToBranch(segmentId);
    },
    [navigateToBranch]
  );

  // Show loading state
  if (isLoadingDetails && !story) {
    return <LoadingState />;
  }

  // Show error state
  if (storyError) {
    return <ErrorState error={storyError} onRetry={fetchStoryDetails} />;
  }

  // Show empty state
  if (!story) {
    return <EmptyState />;
  }

  return (
    <SafeAreaView className="flex-1 bg-background-0">
      <StoryDetailHeader
        story={story}
        isLiked={isLiked}
        likeCount={likeCount}
        onLikeToggle={handleLikeToggle}
        isLiking={isLiking}
      />

      <BreadcrumbNavigation
        storyTitle={story.title}
        currentPath={[]}
        onPathSelect={() => {}}
      />

      <Box className="flex-1">
        <StoryFeed
          segments={segments}
          isLoading={isLoadingSegments}
          onRefresh={refreshSegments}
          onLoadMore={loadMoreSegments}
          onBranchSelect={handleBranchSelect}
          hasMoreSegments={hasMoreSegments}
        />
      </Box>

      <Box className="p-4 border-t border-outline-300">
        <AddSegmentForm
          segmentContent={newSegmentContent}
          onContentChange={setNewSegmentContent}
          onSubmit={submitSegment}
          isSubmitting={isSubmittingSegment}
        />
      </Box>
    </SafeAreaView>
  );
}
