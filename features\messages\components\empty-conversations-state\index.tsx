import React from 'react';
import { useTranslation } from 'react-i18next';
import { MessageSquare, Plus } from 'lucide-react-native';
import { PlaceholderTab } from '@/features/social/components/placeholder-tab';

interface EmptyConversationsStateProps {
  onNewConversation?: () => void;
}

export default function EmptyConversationsState({
  onNewConversation,
}: EmptyConversationsStateProps) {
  const { t } = useTranslation();

  return (
    <PlaceholderTab
      icon={<MessageSquare size={48} className="text-typography-500 dark:text-typography-400" />}
      title={t('messages.emptyState', '暂无消息')}
      subtitle={t(
        'messages.emptyStateSubMessage',
        '与其他用户开始对话，分享你的创作灵感'
      )}
      actionLabel={onNewConversation ? t('messages.newConversation', '新建对话') : undefined}
      onAction={onNewConversation}
      actionIcon={<Plus size={16} className="text-typography-950 dark:text-typography-50" />}
    />
  );
}
