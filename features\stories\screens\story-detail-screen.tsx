import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { StorySegment } from '@/api/stories';

// Hooks
import { useStoryDetails } from '../hooks/use-story-details';
import { useStoryAISuggestions } from '../hooks/use-story-aisuggestions';
import { useStorySegmentManagement } from '../hooks/use-story-segment-management';

// Components
import { StoryDetailLoading } from '../components/story-detail-loading';
import { StoryDetailError } from '../components/story-detail-error';
import { StoryDetailEmpty } from '../components/story-detail-empty';
import { StoryDetailContent } from '../components/story-detail-content';

interface StoryDetailScreenProps {
  storyId: string;
}

export default function StoryDetailScreen({ storyId }: StoryDetailScreenProps) {
  const { t } = useTranslation();

  const {
    story,
    isLoading: isLoadingDetails,
    error: storyError,
    isLiked,
    likeCount,
    isLiking,
    hasMoreSegments,
    isLoadingMore,
    loadMoreSegments,
    fetchStoryDetails,
    handleLikeToggle,
  } = useStoryDetails(storyId, 20); // 每页加载20个段落

  const [segments, setSegments] = useState<StorySegment[]>([]);
  const [currentSegmentId, setCurrentSegmentId] = useState<string | undefined>(
    undefined
  );

  // 当故事加载完成时，初始化段落和当前段落ID
  useEffect(() => {
    if (story?.story_segments) {
      console.log('Story segments loaded:', story.story_segments.length);

      // 打印所有段落的ID和parent_segment_id，帮助调试
      story.story_segments.forEach((segment) => {
        console.log(
          `Segment ID: ${segment.id}, Parent ID: ${
            segment.parent_segment_id || 'ROOT'
          }`
        );
      });

      // 找到根段落（没有父段落的段落）
      const rootSegment = story.story_segments.find(
        (segment) => !segment.parent_segment_id
      );

      if (rootSegment) {
        console.log('Root segment found:', rootSegment.id);
        setCurrentSegmentId(rootSegment.id);
      }

      // 最简单的方法：直接显示所有段落，按创建时间排序
      // 这样可以确保所有段落都被显示，无论它们的父子关系如何
      const sortedSegments = [...story.story_segments].sort(
        (a, b) =>
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      );

      console.log(
        'All segments to display (sorted by time):',
        sortedSegments.length
      );
      console.log(
        'Segment IDs:',
        sortedSegments.map((s) => s.id)
      );

      // 设置所有段落
      setSegments(sortedSegments);
    }
  }, [story?.story_segments]);

  // 处理分支变化
  const handleBranchChange = useCallback(
    (segmentId: string) => {
      if (!story?.story_segments) return;

      console.log('Branch change triggered for segment ID:', segmentId);

      // 找到选中的段落
      const selectedSegment = story.story_segments.find(
        (segment) => segment.id === segmentId
      );
      if (!selectedSegment) {
        console.error('Selected segment not found:', segmentId);
        return;
      }

      // 更新当前段落ID
      setCurrentSegmentId(segmentId);

      // 最简单的方法：继续显示所有段落，但将选中的段落滚动到视图中
      // 这样可以确保所有段落都被显示，无论它们的父子关系如何
      console.log(
        'Continuing to show all segments, but focusing on selected segment'
      );

      // 不需要改变显示的段落，只需要更新当前段落ID
      // 如果需要，可以在这里添加滚动逻辑
    },
    [story?.story_segments]
  );

  const handleSegmentAdded = useCallback((newSegment: StorySegment) => {
    console.log('New segment added:', newSegment.id);

    // 更新当前段落ID为新段落ID
    setCurrentSegmentId(newSegment.id);

    // 将新段落添加到当前显示的段落列表中
    setSegments((prevSegments) => {
      // 按创建时间排序
      return [...prevSegments, newSegment].sort(
        (a, b) =>
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      );
    });

    console.log('Added new segment to the list');
  }, []);

  // 不再需要 buildBranchPath 函数，因为我们现在直接显示所有段落

  const {
    newSegmentContent,
    setNewSegmentContent,
    isSubmittingSegment,
    handleAddSegment,
  } = useStorySegmentManagement({
    storyId,
    parentSegmentId: currentSegmentId, // 传递当前段落ID作为父段落ID
    onSegmentAdded: handleSegmentAdded,
  });

  const {
    aiSegmentSuggestions,
    loadingAISegmentSuggestions,
    showAISegmentSuggestions,
    currentSegmentIsAI,
    setCurrentSegmentIsAI,
    handleFetchAISuggestionsForSegment,
    handleSelectAISuggestion,
  } = useStoryAISuggestions(story);

  const onSelectAISuggestionForForm = (suggestion: string) => {
    handleSelectAISuggestion(suggestion, setNewSegmentContent);
  };

  // 处理创建分支按钮点击
  const handleCreateBranchClick = useCallback(() => {
    // 如果有内容，则显示创建分支表单
    if (newSegmentContent.trim()) {
      // 这里可以实现显示创建分支表单的逻辑
      // 例如，可以使用一个状态来控制CreateBranchForm组件的显示
      // 或者直接调用BranchManager组件的方法
      if (currentSegmentId) {
        // 假设BranchManager组件提供了一个方法来创建分支
        // 这里可以直接调用该方法
        // 或者通过其他方式实现
      }
    }
  }, [newSegmentContent, currentSegmentId]);

  const submitSegment = () => {
    handleAddSegment(currentSegmentIsAI);
    setCurrentSegmentIsAI(false); // Reset AI flag
  };

  if (isLoadingDetails) {
    return <StoryDetailLoading />;
  }

  if (storyError) {
    return <StoryDetailError error={storyError} onRetry={fetchStoryDetails} />;
  }

  if (!story) {
    return <StoryDetailEmpty />;
  }

  return (
    <StoryDetailContent
      story={story}
      segments={segments}
      isLiked={isLiked}
      likeCount={likeCount}
      isLiking={isLiking}
      newSegmentContent={newSegmentContent}
      isSubmittingSegment={isSubmittingSegment}
      showAISegmentSuggestions={showAISegmentSuggestions}
      loadingAISegmentSuggestions={loadingAISegmentSuggestions}
      aiSegmentSuggestions={aiSegmentSuggestions}
      currentSegmentId={currentSegmentId}
      hasMoreSegments={hasMoreSegments}
      isLoadingMore={isLoadingMore}
      onLoadMoreSegments={loadMoreSegments}
      onLikeToggle={handleLikeToggle}
      onContentChange={setNewSegmentContent}
      onSubmitSegment={submitSegment}
      onFetchAISuggestions={handleFetchAISuggestionsForSegment}
      onSelectAISuggestion={onSelectAISuggestionForForm}
      onBranchChange={handleBranchChange}
    />
  );
}
