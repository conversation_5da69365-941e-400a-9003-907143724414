import { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '@/lib/store/auth-store';
import { getCurrentUserProfile, Profile } from '@/api/profiles';
import { getStories, Story as ApiStory } from '@/api/stories';

export const useProfileData = () => {
  const { t } = useTranslation();
  const {
    user: authUser,
    isLoading: authStoreLoading,
    isInitialized: authStoreInitialized,
  } = useAuthStore();

  const [profile, setProfile] = useState<Profile | null>(null);
  const [profileLoading, setProfileLoading] = useState(true);
  const [profileError, setProfileError] = useState<string | null>(null);

  // State for user's stories
  const [userStories, setUserStories] = useState<ApiStory[]>([]);
  const [storiesLoading, setStoriesLoading] = useState(false);
  const [storiesError, setStoriesError] = useState<string | null>(null);

  const fetchProfileData = useCallback(async () => {
    if (!authUser) {
      setProfile(null);
      setProfileLoading(false);
      setProfileError(null);
      return;
    }
    setProfileLoading(true);
    setProfileError(null);
    const { data, error } = await getCurrentUserProfile();
    if (error) {
      console.error('Error fetching profile:', error);
      setProfileError(
        t('profileErrors.fetchFailed', 'Failed to load profile.')
      );
      setProfile(null);
    } else {
      setProfile(data);
    }
    setProfileLoading(false);
  }, [authUser, t]);

  useEffect(() => {
    // Fetch profile only when auth state is initialized
    if (authStoreInitialized) {
      fetchProfileData();
    }
  }, [authStoreInitialized, fetchProfileData]);

  // Fetch user's stories when authUser is available
  useEffect(() => {
    const fetchUserStories = async () => {
      if (!authUser) {
        setUserStories([]);
        setStoriesLoading(false);
        return;
      }
      setStoriesLoading(true);
      setStoriesError(null);
      try {
        // Fetch published stories by the user. Can add options for drafts later.
        const { data, error } = await getStories({
          authorId: authUser.id,
          filter: 'my_published',
        });
        if (error) {
          throw new Error(
            error.message || t('profileErrors.fetchStoriesFailed')
          );
        }
        setUserStories(data || []);
        console.log('Fetched user stories:', data);
      } catch (e: any) {
        console.error("Error fetching user's stories:", e);
        setStoriesError(e.message || t('profileErrors.fetchStoriesFailed'));
        setUserStories([]);
      } finally {
        setStoriesLoading(false);
      }
    };

    if (authStoreInitialized && authUser) {
      fetchUserStories();
    }
  }, [authStoreInitialized, authUser, t]);

  // Combine loading states
  const isLoading = authStoreLoading || profileLoading || storiesLoading;

  return {
    profile,
    profileLoading,
    profileError,
    userStories,
    storiesLoading,
    storiesError,
    isLoading,
    authUser,
    authStoreInitialized,
    fetchProfileData,
  };
};
