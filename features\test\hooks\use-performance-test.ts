import { useState, useEffect, useCallback } from 'react';
import { generateMockStories } from '@/utils/mock-data';
import { Story } from '@/api/stories/types';
import { StoryTabKey } from '@/features/stories/components/story-tabs';

export function usePerformanceTest() {
  // State
  const [activeTab, setActiveTab] = useState<StoryTabKey>('drafts');
  const [stories, setStories] = useState<Story[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [dataSetSize, setDataSetSize] = useState(10);
  const [renderTime, setRenderTime] = useState(0);
  const [memoryUsage, setMemoryUsage] = useState('N/A');
  
  // Data set size options
  const dataSizes = [10, 50, 100, 200, 500, 1000];
  
  // Load stories with the selected data set size
  const loadStories = useCallback(async () => {
    setIsLoading(true);
    
    // Record start time
    const startTime = performance.now();
    
    // Generate mock stories
    const mockStories = generateMockStories(dataSetSize);
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Set stories
    setStories(mockStories);
    
    // Record end time
    const endTime = performance.now();
    setRenderTime(endTime - startTime);
    
    // Try to get memory usage (only works in development)
    try {
      if (global.performance && global.performance.memory) {
        // @ts-ignore - This is a non-standard API
        const memory = global.performance.memory;
        setMemoryUsage(`${Math.round(memory.usedJSHeapSize / 1024 / 1024)} MB`);
      } else {
        setMemoryUsage('Not available');
      }
    } catch (error) {
      setMemoryUsage('Not available');
    }
    
    setIsLoading(false);
  }, [dataSetSize]);
  
  // Load stories when data set size changes
  useEffect(() => {
    loadStories();
  }, [dataSetSize, loadStories]);
  
  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadStories();
    setIsRefreshing(false);
  };
  
  // Handle story press
  const handleStoryPress = (storyId: string) => {
    console.log('Story pressed:', storyId);
    // In a test environment, we don't navigate
  };

  return {
    activeTab,
    setActiveTab,
    stories,
    isLoading,
    isRefreshing,
    dataSetSize,
    setDataSetSize,
    renderTime,
    memoryUsage,
    dataSizes,
    handleRefresh,
    handleStoryPress,
  };
}
