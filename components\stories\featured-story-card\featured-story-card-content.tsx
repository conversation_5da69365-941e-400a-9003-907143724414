import React from 'react';
import { Text } from '@/components/ui/text';
import { HStack } from '@/components/ui/hstack';
import { Box } from '@/components/ui/box';

interface FeaturedStoryCardContentProps {
  summary: string;
  themeTags: string[];
}

export function FeaturedStoryCardContent({
  summary,
  themeTags,
}: FeaturedStoryCardContentProps) {
  return (
    <>
      <Text className="text-sm text-white mb-3 line-clamp-2">{summary}</Text>

      <HStack className="flex-wrap mb-4 gap-2">
        {themeTags.map((tag) => (
          <Box key={tag} className="bg-white/20 px-2 py-1 rounded-full">
            <Text className="text-xs text-white">{tag}</Text>
          </Box>
        ))}
      </HStack>
    </>
  );
}
