import React, { useState } from 'react';
import { <PERSON>ert } from 'react-native';
import { <PERSON>, useRouter } from 'expo-router';
import { resetPassword } from '@/api/auth';
import { useTranslation } from 'react-i18next';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Button, ButtonText, ButtonSpinner } from '@/components/ui/m3e-button';
import { Input, InputField } from '@/components/ui/input';

/**
 * ResetPasswordScreen component
 *
 * This screen allows users to request a password reset by entering their email address.
 * It sends a reset password link to the provided email.
 */
export default function ResetPasswordScreen() {
  const { t } = useTranslation();
  const router = useRouter();

  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);

  // Email validation regex
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  /**
   * Handles the password reset request
   */
  const handleResetPassword = async () => {
    // Validate email
    if (!email) {
      Alert.alert(t('auth.errorTitle'), t('auth.fieldRequired'));
      return;
    }

    if (!emailRegex.test(email)) {
      Alert.alert(t('auth.errorTitle'), t('auth.invalidEmail'));
      return;
    }

    setLoading(true);
    try {
      const { error } = await resetPassword(email);
      setLoading(false);

      if (error) {
        console.error('Reset password error:', error);
        Alert.alert(
          t('auth.errorTitle'),
          error.message || t('auth.resetPasswordFailed')
        );
      } else {
        Alert.alert(t('auth.successTitle'), t('auth.resetPasswordSuccess'), [
          {
            text: 'OK',
            onPress: () => router.replace('/(auth)/login'),
          },
        ]);
      }
    } catch (error) {
      setLoading(false);
      console.error('Reset password error:', error);
      Alert.alert(t('auth.errorTitle'), t('auth.networkError'));
    }
  };

  return (
    <Box className="flex-1 p-6 bg-background-50 dark:bg-background-900 justify-center">
      <Text className="text-xl font-bold text-typography-900 dark:text-typography-100 mb-6 text-center">
        {t('auth.resetPasswordTitle')}
      </Text>

      <Text className="text-base text-typography-700 dark:text-typography-300 mb-8 text-center">
        {t('auth.resetPasswordInstructions')}
      </Text>

      <Input className="w-full mb-4">
        <InputField
          placeholder={t('auth.emailLabel')}
          keyboardType="email-address"
          autoCapitalize="none"
          value={email}
          onChangeText={setEmail}
          className="p-3 border border-outline-300 dark:border-outline-600 rounded-md bg-background-50 dark:bg-background-800 text-typography-900 dark:text-typography-100"
        />
      </Input>

      <Button
        action="primary"
        variant="solid"
        size="md"
        onPress={handleResetPassword}
        isDisabled={loading}
        className="w-full py-3 rounded-md mt-3 mb-6 min-h-[50px] justify-center"
      >
        {loading ? (
          <ButtonSpinner color="$background" />
        ) : (
          <ButtonText>{t('auth.resetPasswordButton')}</ButtonText>
        )}
      </Button>

      <Box className="flex-row justify-center mt-4">
        <Text className="text-typography-700 dark:text-typography-300 mr-2">
          {t('auth.switchToLogin')}
        </Text>
        <Link href="/(auth)/login">
          <Text className="text-primary-600 dark:text-primary-400 font-medium">
            {t('auth.loginButton')}
          </Text>
        </Link>
      </Box>
    </Box>
  );
}
