import React, { useState } from 'react';
import { Alert } from 'react-native';
import { Link, router } from 'expo-router';
import { signInWithEmail } from '@/api/auth';
import { useAuthStore } from '@/lib/store/auth-store';
import { useTranslation } from 'react-i18next';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Button, ButtonText, ButtonSpinner } from '@/components/ui/m3e-button';
import { Input, InputField } from '@/components/ui/input';
import { Pressable } from '@/components/ui/pressable';

export default function LoginScreen() {
  const { t } = useTranslation();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const { signIn } = useAuthStore(); // Get the signIn action from auth store

  const handleLogin = async () => {
    console.log('[LoginAttempt] Attempting to login with email:', email);
    setLoading(true);
    try {
      const { data, error } = await signInWithEmail(email, password);
      // Log the full data and error objects for detailed inspection
      console.log('[LoginResponse] Data:', JSON.stringify(data, null, 2));
      console.log('[LoginResponse] Error:', JSON.stringify(error, null, 2));
      setLoading(false);

      if (error) {
        console.error('[LoginError] Supabase Auth Error:', error);
        // Provide a fallback message for t() in case the key is missing
        Alert.alert(
          t('loginErrorTitle', 'Login Error'),
          error.message ||
            t('authErrors.unknownError', 'An unknown error occurred.')
        );
      } else if (data?.user && data?.session) {
        console.log(
          '[LoginSuccess] User and session received. User ID:',
          data.user.id
        );
        signIn(data.session, data.user);
        // Redirection is handled by _layout.tsx based on auth state
      } else {
        console.warn(
          '[LoginFailed] No error, but no user/session data either. Data:',
          JSON.stringify(data, null, 2)
        );
        Alert.alert(
          t('loginErrorTitle', 'Login Error'),
          t('authErrors.loginFailed', 'Login failed. Please try again.')
        );
      }
    } catch (e: any) {
      console.error('[LoginCatch] Unexpected error during login:', e);
      setLoading(false);
      Alert.alert(
        t('unexpectedErrorTitle', 'Unexpected Error'),
        e.message ||
          t('authErrors.unexpectedError', 'An unexpected error occurred.')
      );
    }
  };

  return (
    <Box className="flex-1 justify-center items-center p-4 bg-background-50 dark:bg-background-900">
      <Text className="text-2xl font-bold text-typography-900 dark:text-typography-100 mb-6">
        {t('loginTitle')}
      </Text>

      <Input className="w-full mb-4">
        <InputField
          placeholder={t('emailPlaceholder')}
          keyboardType="email-address"
          autoCapitalize="none"
          value={email}
          onChangeText={setEmail}
          className="p-3 border border-outline-300 dark:border-outline-600 rounded-md bg-background-50 dark:bg-background-800 text-typography-900 dark:text-typography-100"
        />
      </Input>

      <Input className="w-full mb-4">
        <InputField
          placeholder={t('passwordPlaceholder')}
          secureTextEntry
          value={password}
          onChangeText={setPassword}
          className="p-3 border border-outline-300 dark:border-outline-600 rounded-md bg-background-50 dark:bg-background-800 text-typography-900 dark:text-typography-100"
        />
      </Input>

      <Pressable
        className="self-end mb-4"
        onPress={() => router.push('/(auth)/reset-password')}
      >
        <Text className="text-primary-600 dark:text-primary-400 text-sm font-medium">
          {t('auth.forgotPasswordLink')}
        </Text>
      </Pressable>

      <Button
        action="primary"
        variant="solid"
        size="md"
        onPress={handleLogin}
        isDisabled={loading}
        className="w-full py-3 rounded-md"
      >
        {loading ? (
          <ButtonSpinner color="$background" />
        ) : (
          <ButtonText>{t('loginButton')}</ButtonText>
        )}
      </Button>

      <Box className="flex-row mt-4">
        <Text className="text-typography-700 dark:text-typography-300 text-sm">
          {t('noAccountText')}
        </Text>
        <Link href="/(auth)/register">
          <Text className="text-primary-600 dark:text-primary-400 text-sm font-bold ml-1">
            {t('signupLinkText')}
          </Text>
        </Link>
      </Box>
    </Box>
  );
}
