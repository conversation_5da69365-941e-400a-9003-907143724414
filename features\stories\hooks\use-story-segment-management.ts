import { useState } from 'react';
import { Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import { addStorySegment, StorySegment } from '@/api/stories';
import { useAuthStore } from '@/lib/store/auth-store';

interface UseStorySegmentManagementProps {
  storyId: string;
  parentSegmentId?: string; // 添加父段落ID参数
  onSegmentAdded?: (newSegment: StorySegment) => void; // Callback after segment is successfully added
}

export function useStorySegmentManagement({
  storyId,
  parentSegmentId,
  onSegmentAdded,
}: UseStorySegmentManagementProps) {
  const { t } = useTranslation();
  const currentUser = useAuthStore((state) => state.user);

  const [newSegmentContent, setNewSegmentContent] = useState('');
  const [isSubmittingSegment, setIsSubmittingSegment] = useState(false);

  const handleAddSegment = async (isAiAssisted: boolean = false) => {
    if (!newSegmentContent.trim()) {
      Alert.alert(
        t('storyDetail.errors.title', 'Error'),
        t('storyDetail.errors.emptySegment', 'Cannot add an empty segment.')
      );
      return;
    }
    if (!currentUser) {
      Alert.alert(
        t('storyDetail.errors.title', 'Error'),
        t(
          'storyDetail.errors.notLoggedInSegment',
          'You must be logged in to add to the story.'
        )
      );
      return;
    }
    if (!storyId) return;

    setIsSubmittingSegment(true);
    try {
      const { data: newSegment, error: segmentError } = await addStorySegment(
        storyId,
        newSegmentContent.trim(),
        'text', // contentType
        parentSegmentId, // 使用传入的父段落ID
        isAiAssisted // Pass the AI contribution flag
      );

      if (segmentError) throw segmentError;

      if (newSegment) {
        if (onSegmentAdded) {
          onSegmentAdded(newSegment);
        }
        setNewSegmentContent(''); // Reset input
        // Optionally provide success feedback, Alert might be disruptive
        // Alert.alert(t('storyDetail.success.title', 'Success'), t('storyDetail.success.segmentAdded', 'Your part has been added!'));
      }
    } catch (e: any) {
      console.error('Failed to add segment:', e);
      Alert.alert(
        t('storyDetail.errors.title', 'Error'),
        e.message ||
          t(
            'storyDetail.errors.segmentAddFailed',
            'Failed to add your part. Please try again.'
          )
      );
    } finally {
      setIsSubmittingSegment(false);
    }
  };

  return {
    newSegmentContent,
    setNewSegmentContent,
    isSubmittingSegment,
    handleAddSegment,
  };
}
