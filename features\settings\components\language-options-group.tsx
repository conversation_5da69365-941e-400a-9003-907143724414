import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSettingsStore } from '@/lib/store/settings-store';
import { Box } from '@/components/ui/box';
import { Pressable } from '@/components/ui/pressable/index';
import { Text } from '@/components/ui/text';
import { useColorScheme } from 'nativewind';

export function LanguageOptionsGroup() {
  const { t } = useTranslation('settings');
  const language = useSettingsStore((state) => state.language);
  const setLanguage = useSettingsStore((state) => state.setLanguage);
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Define language options
  const languageOptions: { value: string; label: string }[] = [
    { value: 'en', label: t('english') },
    { value: 'zh', label: t('chinese') },
  ];

  return (
    <Box className="w-full flex-row">
      {languageOptions.map((option) => (
        <Pressable
          key={option.value}
          onPress={() => setLanguage(option.value)}
          className={`flex-1 items-center justify-center py-4 ${
            language === option.value
              ? 'bg-primary-500'
              : isDark
              ? 'bg-background-800'
              : 'bg-background-200'
          }`}
        >
          <Text
            className={`font-medium ${
              language === option.value
                ? 'text-white' // 选中项始终使用白色文本
                : isDark
                ? 'text-typography-50'
                : 'text-typography-900'
            }`}
          >
            {option.label}
          </Text>
        </Pressable>
      ))}
    </Box>
  );
}
