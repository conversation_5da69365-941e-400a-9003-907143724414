import React from 'react';
import { Stack, useLocalSearchParams } from 'expo-router';
import { useTranslation } from 'react-i18next';
import UserProfileScreen from '@/features/social/screens/user-profile-screen';
import { Text } from '@/components/ui/text';
import { Box } from '@/components/ui/box';

export default function UserProfilePageRoute() {
  const { id } = useLocalSearchParams<{ id?: string }>();
  const { t } = useTranslation();

  if (!id) {
    return (
      <Box className="flex-1 bg-background-100 items-center justify-center">
        <Text>{t('userProfile.errors.idNotFound', '用户ID未找到')}</Text>
      </Box>
    );
  }

  return (
    <>
      <Stack.Screen
        options={{
          title: t('userProfile.title', '用户资料'),
          headerShown: true,
        }}
      />
      <UserProfileScreen userId={id} />
    </>
  );
}
