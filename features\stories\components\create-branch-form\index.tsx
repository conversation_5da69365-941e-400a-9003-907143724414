import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>iew } from 'react-native';
import { useTranslation } from 'react-i18next';
import { X, GitBranch, Sparkles } from 'lucide-react-native';
import { useStoryOptimization } from '@/features/stories/hooks/use-story-optimization';
import StoryOptimizationBlock from '@/features/stories/components/story-optimization-block';

// Import gluestack-ui components
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Button } from '@/components/ui/m3e-button';
import { ButtonIcon } from '@/components/ui/m3e-button';
import { ButtonText } from '@/components/ui/m3e-button';
import { Spinner } from '@/components/ui/spinner';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';
import { Input } from '@/components/ui/input';
import { InputField } from '@/components/ui/input';
import { <PERSON><PERSON><PERSON> } from '@/components/ui/textarea';
import { TextAreaInput } from '@/components/ui/textarea';

interface CreateBranchFormProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (
    content: string,
    branchTitle?: string,
    isAiGenerated?: boolean
  ) => Promise<void>;
  parentSegmentId: string;
  isSubmitting: boolean;
  onRequestAiSuggestion?: () => Promise<string | null>;
}

export default function CreateBranchForm({
  visible,
  onClose,
  onSubmit,
  parentSegmentId,
  isSubmitting,
  onRequestAiSuggestion,
}: CreateBranchFormProps) {
  const { t } = useTranslation();

  const [branchTitle, setBranchTitle] = useState('');
  const [branchContent, setBranchContent] = useState('');
  const [isGeneratingAiSuggestion, setIsGeneratingAiSuggestion] =
    useState(false);
  const [isAIGenerated, setIsAIGenerated] = useState(false);

  // 添加 AI 优化功能
  const { isOptimizing, handleOptimizeContent } = useStoryOptimization({
    onOptimizedContent: (optimizedContent) => {
      setBranchContent(optimizedContent);
      setIsAIGenerated(true);
    },
  });

  // 处理提交
  const handleSubmit = async () => {
    if (!branchContent.trim()) return;
    await onSubmit(
      branchContent.trim(),
      branchTitle.trim() || undefined,
      isAIGenerated
    );
    resetForm();
  };

  // 处理 AI 优化
  const handleOptimize = (contentToOptimize: string, type: any) => {
    handleOptimizeContent(contentToOptimize);
  };

  // 处理AI建议
  const handleAiSuggestion = async () => {
    if (!onRequestAiSuggestion) return;

    setIsGeneratingAiSuggestion(true);
    try {
      const suggestion = await onRequestAiSuggestion();
      if (suggestion) {
        setBranchContent(suggestion);
      }
    } finally {
      setIsGeneratingAiSuggestion(false);
    }
  };

  // 重置表单
  const resetForm = () => {
    setBranchTitle('');
    setBranchContent('');
    setIsAIGenerated(false);
  };

  // 关闭模态框
  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={handleClose}
    >
      <Box className="flex-1 justify-center items-center bg-black/50">
        <Box className="w-[90%] max-w-md bg-background-50 dark:bg-background-900 rounded-xl overflow-hidden">
          <Box className="flex-row items-center justify-between p-4 border-b border-outline-200 dark:border-outline-700 bg-surface-50 dark:bg-surface-900">
            <Text className="text-lg font-bold text-typography-900 dark:text-typography-100">
              {t('storyDetail.createBranch', 'Create New Branch')}
            </Text>
            <Button
              variant="link"
              action="secondary"
              onPress={handleClose}
              isDisabled={isSubmitting}
              className="p-1"
            >
              <ButtonIcon as={X} size="md" />
            </Button>
          </Box>

          <ScrollView className="max-h-[400px]">
            <Box className="p-4">
              <Text className="text-sm font-medium text-typography-900 dark:text-typography-100 mb-2">
                {t('storyDetail.branchTitle', 'Branch Title (Optional)')}
              </Text>
              <Input size="md" className="mb-4">
                <InputField
                  placeholder={t(
                    'storyDetail.branchTitlePlaceholder',
                    'Enter a title for this branch'
                  )}
                  value={branchTitle}
                  onChangeText={setBranchTitle}
                  maxLength={50}
                  isDisabled={isSubmitting}
                />
              </Input>

              <Text className="text-sm font-medium text-typography-900 dark:text-typography-100 mb-2">
                {t('storyDetail.branchContent', 'Branch Content')}
              </Text>
              <TextArea size="md" className="mb-4">
                <TextAreaInput
                  placeholder={t(
                    'storyDetail.branchContentPlaceholder',
                    'Continue the story...'
                  )}
                  value={branchContent}
                  onChangeText={setBranchContent}
                  isDisabled={isSubmitting || isGeneratingAiSuggestion}
                  className="min-h-[150px]"
                />
              </TextArea>

              {onRequestAiSuggestion && (
                <Button
                  action="primary"
                  variant="solid"
                  className="mb-4 flex-row items-center justify-center"
                  onPress={handleAiSuggestion}
                  isDisabled={isSubmitting || isGeneratingAiSuggestion}
                >
                  {isGeneratingAiSuggestion ? (
                    <Spinner size="small" color="$background50" />
                  ) : (
                    <>
                      <ButtonIcon as={Sparkles} size="sm" />
                      <ButtonText className="ml-1">
                        {t('storyDetail.getAiSuggestion', 'Get AI Suggestion')}
                      </ButtonText>
                    </>
                  )}
                </Button>
              )}

              {/* AI 优化按钮 */}
              <StoryOptimizationBlock
                onOptimizeContent={handleOptimize}
                isOptimizing={isOptimizing}
                disabled={isSubmitting}
              />
            </Box>
          </ScrollView>

          <HStack className="p-4 border-t border-outline-200 dark:border-outline-700 bg-surface-50 dark:bg-surface-900 justify-between">
            <Button
              variant="outline"
              action="secondary"
              size="md"
              className="flex-1 mr-2"
              onPress={handleClose}
              isDisabled={isSubmitting}
            >
              <ButtonText>{t('cancel', 'Cancel')}</ButtonText>
            </Button>

            <Button
              variant="solid"
              action="primary"
              size="md"
              className="flex-1 ml-2"
              onPress={handleSubmit}
              isDisabled={!branchContent.trim() || isSubmitting}
            >
              {isSubmitting ? (
                <Spinner size="small" color="$background50" />
              ) : (
                <>
                  <ButtonIcon as={GitBranch} size="sm" />
                  <ButtonText className="ml-1">
                    {t('storyDetail.createBranchButton', 'Create Branch')}
                  </ButtonText>
                </>
              )}
            </Button>
          </HStack>
        </Box>
      </Box>
    </Modal>
  );
}
