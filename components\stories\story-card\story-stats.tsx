import React from 'react';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Box } from '@/components/ui/box';
import { Eye, Heart } from 'lucide-react-native';
import { useColorScheme } from 'nativewind';

interface StoryStatsProps {
  views: number;
  likes: number;
}

export function StoryStats({ views, likes }: StoryStatsProps) {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const iconColor = isDark ? '#A1A1AA' : '#71717A'; // typography-400 or typography-500

  return (
    <HStack className="mt-1 space-x-4">
      <HStack className="items-center space-x-1">
        <Eye size={12} color={iconColor} />
        <Text className="text-xs text-typography-500 dark:text-typography-400">
          {views}
        </Text>
      </HStack>

      <HStack className="items-center space-x-1">
        <Heart size={12} color={iconColor} />
        <Text className="text-xs text-typography-500 dark:text-typography-400">
          {likes}
        </Text>
      </HStack>
    </HStack>
  );
}
