import React, { useState } from 'react';
import {
  ScrollView,
  View,
  Text,
  StyleSheet,
  Modal,
  Pressable,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAppTheme } from '@/hooks/use-app-theme';
import {
  M3EButton,
  M3EButtonGroups,
  M3EFAB,
  M3EFABMenu,
  M3EIconButton,
  M3ESplitButton,
  type M3EFABMenuItem,
} from '@/components/ui/m3e-button';
import { M3ENavigationBar } from '@/components/ui/m3e-navigation-bar';
import { M3EAppBar } from '@/components/ui/m3e-app-bar';
import { M3ECheckbox } from '@/components/ui/m3e-checkbox';
import { M3EChip, M3EChipGroup } from '@/components/ui/m3e-chips';
import { M3ETimePicker } from '@/components/ui/m3e-time-picker';
import { M3ESlider } from '@/components/ui/m3e-slider';
import { M3ESnackbar } from '@/components/ui/m3e-snackbar';
import { M3ESwitch } from '@/components/ui/m3e-switch';
import { M3ETabs } from '@/components/ui/m3e-tabs';
import { M3ETextField } from '@/components/ui/m3e-text-field';
import { M3EToolbar } from '@/components/ui/m3e-toolbar';
import { M3ETooltip } from '@/components/ui/m3e-tooltip';
import { M3EDatePicker } from '@/components/ui/m3e-date-picker';
import { Ionicons } from '@expo/vector-icons';

interface M3EDemoModalProps {
  visible: boolean;
  onClose: () => void;
}

export default function M3EDemoModal({ visible, onClose }: M3EDemoModalProps) {
  const theme = useAppTheme();
  const [selectedGroupIndex, setSelectedGroupIndex] = useState(0);
  const [isFABMenuOpen, setIsFABMenuOpen] = useState(false);
  const [checkboxStates, setCheckboxStates] = useState({
    basic: false,
    indeterminate: true,
    error: false,
  });
  const [selectedTime, setSelectedTime] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedChips, setSelectedChips] = useState([0]);
  const [sliderValue, setSliderValue] = useState(50);
  const [showSnackbar, setShowSnackbar] = useState(false);
  const [switchValue, setSwitchValue] = useState(false);
  const [activeTab, setActiveTab] = useState('tab1');
  const [textFieldValue, setTextFieldValue] = useState('');
  const [showTooltip, setShowTooltip] = useState(false);

  const isDark = theme.dark;
  const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

  // 响应式尺寸计算
  const isTablet = screenWidth >= 768;
  const isLargeScreen = screenWidth >= 1024;

  const fabMenuItems: M3EFABMenuItem[] = [
    {
      icon: (
        <Ionicons name="add" size={24} color={isDark ? '#4F378A' : '#4F378A'} />
      ),
      label: 'Create',
      onPress: () => console.log('Create pressed'),
    },
    {
      icon: (
        <Ionicons
          name="create-outline"
          size={24}
          color={isDark ? '#4F378A' : '#4F378A'}
        />
      ),
      label: 'Edit',
      onPress: () => console.log('Edit pressed'),
    },
    {
      icon: (
        <Ionicons
          name="share"
          size={24}
          color={isDark ? '#4F378A' : '#4F378A'}
        />
      ),
      label: 'Share',
      onPress: () => console.log('Share pressed'),
    },
  ];

  const styles = StyleSheet.create({
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      padding: isTablet ? 32 : 16,
    },
    modalContainer: {
      width: '100%',
      maxWidth: isLargeScreen ? 800 : isTablet ? 600 : 400,
      height: isTablet ? '85%' : '90%',
      maxHeight: screenHeight - (isTablet ? 64 : 32),
      backgroundColor: isDark ? '#1D1B20' : '#FEF7FF',
      borderRadius: 16,
      overflow: 'hidden',
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: isTablet ? 24 : 16,
      borderBottomWidth: 1,
      borderBottomColor: isDark ? '#49454F' : '#E6E0E9',
    },
    headerTitle: {
      fontSize: isTablet ? 24 : 20,
      fontWeight: '600',
      color: isDark ? '#E6E0E9' : '#1D1B20',
    },
    closeButton: {
      padding: 8,
      borderRadius: 20,
      backgroundColor: isDark ? '#49454F' : '#E6E0E9',
    },
    scrollContent: {
      padding: isTablet ? 24 : 16,
    },
    section: {
      marginBottom: isTablet ? 40 : 32,
    },
    sectionTitle: {
      fontSize: isTablet ? 22 : 18,
      fontWeight: '600',
      color: isDark ? '#E6E0E9' : '#1D1B20',
      marginBottom: isTablet ? 20 : 16,
    },
    subsectionTitle: {
      fontSize: isTablet ? 18 : 16,
      fontWeight: '500',
      color: isDark ? '#E6E0E9' : '#1D1B20',
      marginBottom: isTablet ? 16 : 12,
      marginTop: isTablet ? 20 : 16,
    },
    buttonRow: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: isTablet ? 16 : 12,
      marginBottom: isTablet ? 20 : 16,
    },
    fabContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: isTablet ? 20 : 16,
      marginBottom: isTablet ? 20 : 16,
    },
    fabMenuContainer: {
      position: 'relative',
      alignItems: 'center',
      justifyContent: 'center',
      height: 200,
      marginBottom: 16,
    },
  });

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.headerTitle}>M3E 组件演示</Text>
            <Pressable style={styles.closeButton} onPress={onClose}>
              <Ionicons
                name="close"
                size={20}
                color={isDark ? '#E6E0E9' : '#1D1B20'}
              />
            </Pressable>
          </View>

          {/* Content */}
          <ScrollView style={styles.scrollContent}>
            {/* M3E Buttons */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>M3E Buttons</Text>

              <Text style={styles.subsectionTitle}>Filled Buttons</Text>
              <View style={styles.buttonRow}>
                <M3EButton
                  variant="filled"
                  size="small"
                  onPress={() => console.log('Small filled pressed')}
                >
                  Small
                </M3EButton>
                <M3EButton
                  variant="filled"
                  size="medium"
                  onPress={() => console.log('Medium filled pressed')}
                >
                  Medium
                </M3EButton>
                <M3EButton
                  variant="filled"
                  size="large"
                  onPress={() => console.log('Large filled pressed')}
                >
                  Large
                </M3EButton>
              </View>

              <Text style={styles.subsectionTitle}>Outlined Buttons</Text>
              <View style={styles.buttonRow}>
                <M3EButton
                  variant="outlined"
                  size="small"
                  onPress={() => console.log('Small outlined pressed')}
                >
                  Small
                </M3EButton>
                <M3EButton
                  variant="outlined"
                  size="medium"
                  onPress={() => console.log('Medium outlined pressed')}
                >
                  Medium
                </M3EButton>
                <M3EButton
                  variant="outlined"
                  size="large"
                  onPress={() => console.log('Large outlined pressed')}
                >
                  Large
                </M3EButton>
              </View>

              <Text style={styles.subsectionTitle}>Text Buttons</Text>
              <View style={styles.buttonRow}>
                <M3EButton
                  variant="text"
                  size="small"
                  onPress={() => console.log('Small text pressed')}
                >
                  Small
                </M3EButton>
                <M3EButton
                  variant="text"
                  size="medium"
                  onPress={() => console.log('Medium text pressed')}
                >
                  Medium
                </M3EButton>
                <M3EButton
                  variant="text"
                  size="large"
                  onPress={() => console.log('Large text pressed')}
                >
                  Large
                </M3EButton>
              </View>

              <Text style={styles.subsectionTitle}>With Icons</Text>
              <View style={styles.buttonRow}>
                <M3EButton
                  variant="filled"
                  size="medium"
                  icon="add"
                  onPress={() => console.log('Icon button pressed')}
                >
                  Add Item
                </M3EButton>
                <M3EButton
                  variant="outlined"
                  size="medium"
                  icon="download"
                  onPress={() => console.log('Download pressed')}
                >
                  Download
                </M3EButton>
              </View>
            </View>

            {/* M3E Button Groups */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>M3E Button Groups</Text>

              <Text style={styles.subsectionTitle}>Round Button Groups</Text>
              <View style={styles.buttonRow}>
                <M3EButtonGroups
                  type="round"
                  size="medium"
                  slots={3}
                  labels={['First', 'Second', 'Third']}
                  selectedIndex={selectedGroupIndex}
                  onPress={setSelectedGroupIndex}
                />
              </View>

              <Text style={styles.subsectionTitle}>Square Button Groups</Text>
              <View style={styles.buttonRow}>
                <M3EButtonGroups
                  type="square"
                  size="medium"
                  slots={4}
                  labels={['Option A', 'Option B', 'Option C', 'Option D']}
                  selectedIndex={0}
                  onPress={(index) =>
                    console.log('Square group pressed:', index)
                  }
                />
              </View>
            </View>

            {/* M3E FAB */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>M3E FAB</Text>

              <Text style={styles.subsectionTitle}>Standard FABs</Text>
              <View style={styles.fabContainer}>
                <M3EFAB
                  size="small"
                  variant="primary"
                  icon={<Ionicons name="add" size={18} color="#FFFFFF" />}
                  onPress={() => console.log('Small FAB pressed')}
                />
                <M3EFAB
                  size="medium"
                  variant="primary"
                  icon={<Ionicons name="add" size={24} color="#FFFFFF" />}
                  onPress={() => console.log('Medium FAB pressed')}
                />
                <M3EFAB
                  size="large"
                  variant="primary"
                  icon={<Ionicons name="add" size={36} color="#FFFFFF" />}
                  onPress={() => console.log('Large FAB pressed')}
                />
              </View>
            </View>

            {/* M3E Icon Button */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>M3E Icon Button</Text>

              <Text style={styles.subsectionTitle}>Standard Icon Buttons</Text>
              <View style={styles.buttonRow}>
                <M3EIconButton
                  variant="standard"
                  size="small"
                  icon={
                    <Ionicons
                      name="heart-outline"
                      size={20}
                      color={isDark ? '#CAC4D0' : '#49454F'}
                    />
                  }
                  onPress={() => console.log('Standard small pressed')}
                />
                <M3EIconButton
                  variant="standard"
                  size="medium"
                  icon={
                    <Ionicons
                      name="heart-outline"
                      size={24}
                      color={isDark ? '#CAC4D0' : '#49454F'}
                    />
                  }
                  onPress={() => console.log('Standard medium pressed')}
                />
              </View>
            </View>

            {/* M3E Checkboxes */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>M3E Checkboxes</Text>

              <Text style={styles.subsectionTitle}>Basic Checkboxes</Text>
              <View style={styles.buttonRow}>
                <M3ECheckbox
                  checked={checkboxStates.basic}
                  onValueChange={(checked) =>
                    setCheckboxStates((prev) => ({ ...prev, basic: checked }))
                  }
                  label="基础复选框"
                />
                <M3ECheckbox
                  indeterminate={checkboxStates.indeterminate}
                  label="不确定状态"
                />
                <M3ECheckbox
                  checked={checkboxStates.error}
                  error={true}
                  onValueChange={(checked) =>
                    setCheckboxStates((prev) => ({ ...prev, error: checked }))
                  }
                  label="错误状态"
                />
              </View>
            </View>

            {/* M3E Chips */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>M3E Chips</Text>

              <Text style={styles.subsectionTitle}>Chip Group</Text>
              <View style={styles.buttonRow}>
                <M3EChipGroup
                  chips={[
                    { label: '选项 1', variant: 'filter' },
                    { label: '选项 2', variant: 'filter' },
                    { label: '选项 3', variant: 'filter' },
                  ]}
                  selectedIndices={selectedChips}
                  multiSelect={true}
                  onSelectionChange={(indices) =>
                    setSelectedChips(indices as number[])
                  }
                />
              </View>
            </View>

            {/* M3E Date & Time Pickers */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>M3E Date & Time Pickers</Text>

              <Text style={styles.subsectionTitle}>Date Picker</Text>
              <View style={styles.buttonRow}>
                <M3EDatePicker
                  value={selectedDate}
                  onDateChange={setSelectedDate}
                  label="选择日期"
                />
              </View>

              <Text style={styles.subsectionTitle}>Time Picker</Text>
              <View style={styles.buttonRow}>
                <M3ETimePicker
                  value={selectedTime}
                  onTimeChange={setSelectedTime}
                  label="选择时间"
                />
              </View>
            </View>

            {/* M3E Slider */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>M3E Slider</Text>

              <Text style={styles.subsectionTitle}>Horizontal Slider</Text>
              <View style={styles.buttonRow}>
                <M3ESlider
                  value={sliderValue}
                  onValueChange={setSliderValue}
                  minimumValue={0}
                  maximumValue={100}
                  orientation="horizontal"
                  size="medium"
                  showValueIndicator={true}
                />
              </View>
            </View>

            {/* M3E Switch */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>M3E Switch</Text>

              <Text style={styles.subsectionTitle}>Basic Switch</Text>
              <View style={styles.buttonRow}>
                <M3ESwitch
                  value={switchValue}
                  onValueChange={setSwitchValue}
                  showIcon={true}
                />
              </View>
            </View>

            {/* M3E Text Field */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>M3E Text Field</Text>

              <Text style={styles.subsectionTitle}>Outlined Text Field</Text>
              <View style={styles.buttonRow}>
                <M3ETextField
                  label="用户名"
                  placeholder="请输入用户名"
                  value={textFieldValue}
                  onChangeText={setTextFieldValue}
                  variant="outlined"
                  leadingIcon={
                    <Ionicons
                      name="person-outline"
                      size={20}
                      color={isDark ? '#CAC4D0' : '#49454F'}
                    />
                  }
                />
              </View>
            </View>

            {/* M3E Tabs */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>M3E Tabs</Text>

              <Text style={styles.subsectionTitle}>Primary Tabs</Text>
              <View style={styles.buttonRow}>
                <M3ETabs
                  items={[
                    {
                      id: 'tab1',
                      label: '首页',
                      icon: <Ionicons name="home" size={20} />,
                    },
                    {
                      id: 'tab2',
                      label: '搜索',
                      icon: <Ionicons name="search" size={20} />,
                    },
                    {
                      id: 'tab3',
                      label: '个人',
                      icon: <Ionicons name="person" size={20} />,
                    },
                  ]}
                  activeTab={activeTab}
                  onTabChange={setActiveTab}
                  variant="primary"
                  configuration="label-and-icon"
                />
              </View>
            </View>

            {/* M3E Toolbar */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>M3E Toolbar</Text>

              <Text style={styles.subsectionTitle}>Floating Toolbar</Text>
              <View style={styles.buttonRow}>
                <M3EToolbar
                  actions={[
                    {
                      id: 'edit',
                      icon: <Ionicons name="create-outline" size={20} />,
                      onPress: () => console.log('Edit pressed'),
                    },
                    {
                      id: 'share',
                      icon: <Ionicons name="share-outline" size={20} />,
                      onPress: () => console.log('Share pressed'),
                    },
                    {
                      id: 'delete',
                      icon: <Ionicons name="trash-outline" size={20} />,
                      onPress: () => console.log('Delete pressed'),
                    },
                  ]}
                  configuration="floating"
                  orientation="horizontal"
                  type="standard"
                />
              </View>
            </View>

            {/* M3E Tooltip */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>M3E Tooltip</Text>

              <Text style={styles.subsectionTitle}>Plain Tooltip</Text>
              <View style={styles.buttonRow}>
                <M3ETooltip
                  content="这是一个工具提示"
                  visible={showTooltip}
                  onVisibleChange={setShowTooltip}
                >
                  <M3EButton
                    variant="outlined"
                    size="medium"
                    onPress={() => setShowTooltip(!showTooltip)}
                  >
                    显示提示
                  </M3EButton>
                </M3ETooltip>
              </View>
            </View>

            {/* M3E Snackbar */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>M3E Snackbar</Text>

              <Text style={styles.subsectionTitle}>Basic Snackbar</Text>
              <View style={styles.buttonRow}>
                <M3EButton
                  variant="filled"
                  size="medium"
                  onPress={() => setShowSnackbar(true)}
                >
                  显示 Snackbar
                </M3EButton>
              </View>

              <M3ESnackbar
                visible={showSnackbar}
                message="这是一个消息提示"
                actionText="确定"
                onActionPress={() => setShowSnackbar(false)}
                showCloseButton={true}
                onClose={() => setShowSnackbar(false)}
                duration={4000}
              />
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
}
