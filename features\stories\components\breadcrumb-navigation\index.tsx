import React from 'react';
import { ScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import { ChevronRight, Home } from 'lucide-react-native';

// Import gluestack-ui components
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { HStack } from '@/components/ui/hstack';

interface BreadcrumbNavigationProps {
  storyTitle: string;
  currentPath: string[];
  pathNames?: string[];
  onPathSelect: (index: number) => void;
}

export default function BreadcrumbNavigation({
  storyTitle,
  currentPath,
  pathNames = [],
  onPathSelect,
}: BreadcrumbNavigationProps) {
  const { t } = useTranslation();

  // Ensure pathNames has the same length as currentPath
  const displayNames =
    currentPath &&
    Array.isArray(currentPath) &&
    pathNames.length === currentPath.length
      ? pathNames
      : currentPath && Array.isArray(currentPath)
      ? currentPath.map((_, index) =>
          index === 0
            ? t('storyDetail.mainBranch', '主线')
            : t('storyDetail.branch', '分支') + ' ' + (index + 1)
        )
      : [];

  return (
    <Box className="bg-surface-50 dark:bg-surface-900 border-b border-outline-200 dark:border-outline-700 py-2">
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerClassName="px-4 flex-row items-center"
      >
        {/* Story Title */}
        <Pressable
          className="flex-row items-center py-1 px-2 rounded-md"
          onPress={() => onPathSelect(-1)} // -1 indicates the story root
        >
          <Home
            size={16}
            className="text-typography-900 dark:text-typography-100"
          />
          <Text
            className="text-sm font-medium text-typography-900 dark:text-typography-100 ml-1"
            numberOfLines={1}
          >
            {storyTitle}
          </Text>
        </Pressable>

        {/* Path Items */}
        {currentPath &&
          Array.isArray(currentPath) &&
          currentPath.map((pathId, index) => (
            <React.Fragment key={pathId}>
              <ChevronRight
                size={16}
                className="text-secondary-500 dark:text-secondary-400 mx-1"
              />
              <Pressable
                className={`flex-row items-center py-1 px-2 rounded-md ${
                  index === currentPath.length - 1
                    ? 'bg-primary-100 dark:bg-primary-900'
                    : ''
                }`}
                onPress={() => onPathSelect(index)}
              >
                <Text
                  className={`text-sm font-medium ${
                    index === currentPath.length - 1
                      ? 'text-primary-600 dark:text-primary-400'
                      : 'text-typography-900 dark:text-typography-100'
                  }`}
                  numberOfLines={1}
                >
                  {displayNames[index]}
                </Text>
              </Pressable>
            </React.Fragment>
          ))}
      </ScrollView>
    </Box>
  );
}
