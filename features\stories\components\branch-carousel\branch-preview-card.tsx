import React from 'react';
import { Image, Dimensions, Pressable } from 'react-native';
import { useTranslation } from 'react-i18next';
import { MessageCircle, GitBranch } from 'lucide-react-native';
import { StorySegment } from '@/api/stories/types';
import { getChildrenCount, getCountValue } from '@/utils/story-helpers';
import { useTheme } from '@/lib/theme/theme-provider';

// Import gluestack-ui components
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';

interface BranchPreviewCardProps {
  branch: StorySegment;
  onPress: () => void;
  isActive?: boolean;
}

export default function BranchPreviewCard({
  branch,
  onPress,
  isActive = false,
}: BranchPreviewCardProps) {
  const { t } = useTranslation();
  const { themeColors } = useTheme();

  // Screen width for responsive sizing
  const { width } = Dimensions.get('window');
  const cardWidth = width * 0.75; // Card takes 75% of screen width

  // Format content preview
  const getContentPreview = (content: string, maxLength = 120) => {
    if (!content) return '';

    // Remove any markdown or special formatting
    const plainText = content.replace(/\[.*?\]/g, '').trim();

    if (plainText.length <= maxLength) return plainText;
    return plainText.substring(0, maxLength) + '...';
  };

  // Determine if branch has special status
  const getBranchStatus = () => {
    if (branch.is_main_branch) {
      return {
        label: t('storyDetail.mainBranch', '主线'),
        bgColor: 'bg-yellow-100 dark:bg-yellow-900/30',
        textColor: 'text-yellow-700 dark:text-yellow-300',
      };
    }

    if (branch.is_ai_generated) {
      return {
        label: t('storyDetail.aiGenerated', 'AI 精选'),
        bgColor: 'bg-blue-100 dark:bg-blue-900/30',
        textColor: 'text-blue-700 dark:text-blue-300',
      };
    }

    // Check if branch is new (less than 24 hours old)
    const createdAt = new Date(branch.created_at);
    const now = new Date();
    const isNew = now.getTime() - createdAt.getTime() < 24 * 60 * 60 * 1000;

    if (isNew) {
      return {
        label: t('storyDetail.new', '新'),
        bgColor: 'bg-green-100 dark:bg-green-900/30',
        textColor: 'text-green-700 dark:text-green-300',
      };
    }

    return null;
  };

  const branchStatus = getBranchStatus();

  return (
    <Pressable
      style={({ pressed }) => ({
        width: cardWidth,
        marginHorizontal: 8,
        borderRadius: 16,
        backgroundColor: isActive
          ? themeColors.primary.container
          : themeColors.surface.container,
        borderWidth: 2,
        borderColor: isActive
          ? themeColors.primary.main
          : themeColors.outline.variant,
        padding: 16,
        elevation: isActive ? 6 : 3,
        transform: [{ scale: pressed ? 0.98 : 1 }],
        shadowColor: themeColors.primary.main,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: isActive ? 0.15 : 0.08,
        shadowRadius: isActive ? 8 : 4,
      })}
      onPress={onPress}
      className={`${
        isActive
          ? 'bg-primary-50 dark:bg-primary-900/20'
          : 'bg-white dark:bg-gray-900'
      }`}
    >
      {/* Content Preview */}
      <Text
        size="md"
        className={`leading-6 mb-4 ${
          isActive
            ? 'text-primary-900 dark:text-primary-100'
            : 'text-gray-900 dark:text-gray-100'
        }`}
        numberOfLines={4}
      >
        {getContentPreview(branch.content)}
      </Text>

      {/* Author Info */}
      <Box className="flex-row items-center mb-4">
        <Image
          source={
            branch.profiles?.avatar_url
              ? { uri: branch.profiles.avatar_url }
              : require('@/assets/images/default-avatar.png')
          }
          className="w-6 h-6 rounded-full mr-2"
        />
        <Text
          size="sm"
          className={`font-medium ${
            isActive
              ? 'text-primary-900 dark:text-primary-100'
              : 'text-gray-900 dark:text-gray-100'
          }`}
          numberOfLines={1}
        >
          {branch.profiles?.username || t('common.unknownUser', '未知用户')}
        </Text>
      </Box>

      {/* Footer with Stats and Status */}
      <Box className="flex-row justify-between items-center">
        <Box className="flex-row space-x-3">
          <Box className="flex-row items-center space-x-1">
            <MessageCircle
              size={14}
              color={
                isActive
                  ? themeColors.primary.main
                  : themeColors.surface.onVariant
              }
            />
            <Text
              size="xs"
              className={
                isActive
                  ? 'text-primary-700 dark:text-primary-300'
                  : 'text-gray-500 dark:text-gray-400'
              }
            >
              {getCountValue(branch.comment_count)}
            </Text>
          </Box>
          <Box className="flex-row items-center space-x-1">
            <GitBranch
              size={14}
              color={
                isActive
                  ? themeColors.primary.main
                  : themeColors.surface.onVariant
              }
            />
            <Text
              size="xs"
              className={
                isActive
                  ? 'text-primary-700 dark:text-primary-300'
                  : 'text-gray-500 dark:text-gray-400'
              }
            >
              {getChildrenCount(branch.children_count)}
            </Text>
          </Box>
        </Box>

        {branchStatus && (
          <Box className={`px-2 py-1 rounded-full ${branchStatus.bgColor}`}>
            <Text size="xs" className={`font-medium ${branchStatus.textColor}`}>
              {branchStatus.label}
            </Text>
          </Box>
        )}
      </Box>
    </Pressable>
  );
}
