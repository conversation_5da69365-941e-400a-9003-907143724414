import React, { useState } from 'react';
import { Alert } from 'react-native';
import {
  Comment,
  deleteStoryComment,
  updateStoryComment,
} from '@/api/comments';
import { useTranslation } from 'react-i18next';
import { formatDistanceToNow } from 'date-fns';
import { useRouter } from 'expo-router';
import { useAuthStore } from '@/lib/store/auth-store';
import { Ionicons } from '@expo/vector-icons';
import { Box } from '@/components/ui/box';
import { Text } from '@/components/ui/text';
import { Button, ButtonText, ButtonSpinner } from '@/components/ui/m3e-button';
import { Pressable } from '@/components/ui/pressable';
import { Avatar } from '@/components/ui/avatar';
import { Textarea, TextareaInput } from '@/components/ui/textarea';
import { HStack } from '@/components/ui/hstack';
import { VStack } from '@/components/ui/vstack';

interface CommentItemProps {
  comment: Comment;
  onReply: (commentId: string) => void;
  onViewReplies: (commentId: string) => void;
  onCommentDeleted: (commentId: string) => void;
  onCommentUpdated: (comment: Comment) => void;
}

export function CommentItem({
  comment,
  onReply,
  onViewReplies,
  onCommentDeleted,
  onCommentUpdated,
}: CommentItemProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const currentUser = useAuthStore((state) => state.user);

  const [isDeleting, setIsDeleting] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState(comment.content);
  const [isUpdating, setIsUpdating] = useState(false);

  const isOwnComment = currentUser?.id === comment.user_id;
  const hasReplies = (comment.reply_count || 0) > 0;

  const formattedDate = comment.created_at
    ? formatDistanceToNow(new Date(comment.created_at), { addSuffix: true })
    : '';

  const handleUserPress = () => {
    if (comment.profiles?.id) {
      router.push(`/profile/${comment.profiles.id}`);
    }
  };

  const handleReplyPress = () => {
    onReply(comment.id);
  };

  const handleViewRepliesPress = () => {
    onViewReplies(comment.id);
  };

  const handleEditPress = () => {
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditedContent(comment.content);
  };

  const handleSaveEdit = async () => {
    if (editedContent.trim() === '') {
      Alert.alert(
        t('error', '错误'),
        t('comments.emptyCommentError', '评论内容不能为空')
      );
      return;
    }

    setIsUpdating(true);

    try {
      const { data, error } = await updateStoryComment(
        comment.id,
        editedContent
      );

      if (error) throw error;

      if (data) {
        onCommentUpdated(data);
        setIsEditing(false);
      }
    } catch (err) {
      console.error('Failed to update comment:', err);
      Alert.alert(
        t('error', '错误'),
        t('comments.updateError', '更新评论失败，请重试')
      );
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDeletePress = () => {
    Alert.alert(
      t('comments.deleteConfirmTitle', '删除评论'),
      t(
        'comments.deleteConfirmMessage',
        '确定要删除这条评论吗？此操作无法撤销。'
      ),
      [
        {
          text: t('cancel', '取消'),
          style: 'cancel',
        },
        {
          text: t('delete', '删除'),
          style: 'destructive',
          onPress: handleConfirmDelete,
        },
      ]
    );
  };

  const handleConfirmDelete = async () => {
    setIsDeleting(true);

    try {
      const { success, error } = await deleteStoryComment(comment.id);

      if (error) throw error;

      if (success) {
        onCommentDeleted(comment.id);
      }
    } catch (err) {
      console.error('Failed to delete comment:', err);
      Alert.alert(
        t('error', '错误'),
        t('comments.deleteError', '删除评论失败，请重试')
      );
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <Box className="p-4 bg-background-100 dark:bg-background-800 rounded-lg mb-4">
      <HStack className="items-center mb-2">
        <Pressable onPress={handleUserPress}>
          <Avatar
            size={40}
            uri={comment.profiles?.avatar_url || ''}
            username={comment.profiles?.username || ''}
          />
        </Pressable>

        <VStack className="flex-1 ml-2">
          <Pressable onPress={handleUserPress}>
            <Text className="text-sm font-medium text-typography-900 dark:text-typography-100">
              {comment.profiles?.username ||
                t('comments.unknownUser', '未知用户')}
            </Text>
          </Pressable>
          <Text className="text-xs text-typography-500 dark:text-typography-400 mt-0.5">
            {formattedDate}
          </Text>
        </VStack>

        {isOwnComment && !isEditing && (
          <HStack>
            <Pressable
              className="p-1"
              onPress={handleEditPress}
              disabled={isDeleting}
            >
              <Ionicons
                name="pencil-outline"
                size={18}
                className="text-typography-500 dark:text-typography-400"
              />
            </Pressable>

            <Pressable
              className="p-1 ml-1"
              onPress={handleDeletePress}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <ButtonSpinner color="$error" />
              ) : (
                <Ionicons
                  name="trash-outline"
                  size={18}
                  className="text-error-600 dark:text-error-400"
                />
              )}
            </Pressable>
          </HStack>
        )}
      </HStack>

      {isEditing ? (
        <Box className="mt-1">
          <Textarea size="md" h={80}>
            <TextareaInput
              value={editedContent}
              onChangeText={setEditedContent}
              className="p-2 bg-background-50 dark:bg-background-700 rounded text-typography-900 dark:text-typography-100"
              multiline
              autoFocus
              maxLength={1000}
            />
          </Textarea>

          <HStack className="justify-end mt-2">
            <Button
              variant="outline"
              onPress={handleCancelEdit}
              isDisabled={isUpdating}
              className="py-1 px-3 rounded mr-2"
            >
              <ButtonText className="text-typography-500 dark:text-typography-400 text-sm">
                {t('cancel', '取消')}
              </ButtonText>
            </Button>

            <Button
              variant="solid"
              action="primary"
              onPress={handleSaveEdit}
              isDisabled={isUpdating}
              className="py-1 px-3 rounded"
            >
              {isUpdating ? (
                <ButtonSpinner color="$background" />
              ) : (
                <ButtonText className="text-background-50 dark:text-background-950 text-sm">
                  {t('save', '保存')}
                </ButtonText>
              )}
            </Button>
          </HStack>
        </Box>
      ) : (
        <Text className="text-base text-typography-900 dark:text-typography-100 leading-6">
          {comment.content}
        </Text>
      )}

      <HStack className="mt-4">
        <Button variant="link" onPress={handleReplyPress} className="mr-6">
          <ButtonText className="text-primary-600 dark:text-primary-400 text-sm font-medium">
            {t('comments.reply', '回复')}
          </ButtonText>
        </Button>

        {hasReplies && (
          <Button variant="link" onPress={handleViewRepliesPress}>
            <ButtonText className="text-primary-600 dark:text-primary-400 text-sm font-medium">
              {t('comments.viewReplies', '查看回复 ({{count}})', {
                count: comment.reply_count,
              })}
            </ButtonText>
          </Button>
        )}
      </HStack>
    </Box>
  );
}
