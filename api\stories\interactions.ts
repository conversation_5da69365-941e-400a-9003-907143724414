import { supabase } from '@/utils/supabase';
import { PostgrestError } from '@supabase/supabase-js';

/**
 * 分支评论接口
 */
export interface BranchComment {
  id: string;
  segment_id: string;
  user_id: string;
  content: string;
  created_at: string;
  updated_at: string;
  profiles?: {
    id: string;
    username: string | null;
    avatar_url: string | null;
  };
}

/**
 * 分支投票接口
 */
export interface BranchVote {
  id: string;
  segment_id: string;
  user_id: string;
  vote_type: 'up' | 'down';
  created_at: string;
}

/**
 * 分支投票统计接口
 */
export interface BranchVoteStats {
  segment_id: string;
  upvotes: number;
  downvotes: number;
  total: number;
}

/**
 * 获取分支的评论
 * 
 * @param segmentId 段落ID
 * @returns 评论列表
 */
export async function getBranchComments(
  segmentId: string
): Promise<{ data: BranchComment[] | null; error: PostgrestError | null }> {
  try {
    const { data, error } = await supabase
      .from('branch_comments')
      .select(`
        id, segment_id, user_id, content, created_at, updated_at,
        profiles!branch_comments_user_id_fkey ( id, username, avatar_url )
      `)
      .eq('segment_id', segmentId)
      .order('created_at', { ascending: true });

    if (error) {
      throw error;
    }

    return { data: data as BranchComment[], error: null };
  } catch (error) {
    console.error('Error getting branch comments:', error);
    return {
      data: null,
      error: error as PostgrestError,
    };
  }
}

/**
 * 添加分支评论
 * 
 * @param segmentId 段落ID
 * @param content 评论内容
 * @returns 新创建的评论
 */
export async function addBranchComment(
  segmentId: string,
  content: string
): Promise<{ data: BranchComment | null; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  
  if (!user) {
    return {
      data: null,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };
  }

  try {
    const { data, error } = await supabase
      .from('branch_comments')
      .insert({
        segment_id: segmentId,
        user_id: user.id,
        content,
      })
      .select(`
        id, segment_id, user_id, content, created_at, updated_at,
        profiles!branch_comments_user_id_fkey ( id, username, avatar_url )
      `)
      .single();

    if (error) {
      throw error;
    }

    return { data: data as BranchComment, error: null };
  } catch (error) {
    console.error('Error adding branch comment:', error);
    return {
      data: null,
      error: error as PostgrestError,
    };
  }
}

/**
 * 删除分支评论
 * 
 * @param commentId 评论ID
 * @returns 操作结果
 */
export async function deleteBranchComment(
  commentId: string
): Promise<{ success: boolean; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  
  if (!user) {
    return {
      success: false,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };
  }

  try {
    // 首先检查评论是否存在并且用户是否有权限删除
    const { data: comment, error: checkError } = await supabase
      .from('branch_comments')
      .select('id, user_id')
      .eq('id', commentId)
      .single();

    if (checkError || !comment) {
      throw checkError || new Error('Comment not found');
    }

    // 检查用户是否是评论的作者
    if (comment.user_id !== user.id) {
      return {
        success: false,
        error: {
          message: 'Permission denied: You can only delete your own comments',
          details: '',
          hint: '',
          code: '403',
        } as PostgrestError,
      };
    }

    // 删除评论
    const { error } = await supabase
      .from('branch_comments')
      .delete()
      .eq('id', commentId);

    if (error) {
      throw error;
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Error deleting branch comment:', error);
    return {
      success: false,
      error: error as PostgrestError,
    };
  }
}

/**
 * 获取分支的投票统计
 * 
 * @param segmentId 段落ID
 * @returns 投票统计
 */
export async function getBranchVoteStats(
  segmentId: string
): Promise<{ data: BranchVoteStats | null; error: PostgrestError | null }> {
  try {
    // 获取投票统计
    const { data, error } = await supabase
      .from('branch_votes')
      .select('vote_type')
      .eq('segment_id', segmentId);

    if (error) {
      throw error;
    }

    // 计算投票统计
    const upvotes = data.filter(vote => vote.vote_type === 'up').length;
    const downvotes = data.filter(vote => vote.vote_type === 'down').length;
    const total = upvotes - downvotes;

    return {
      data: {
        segment_id: segmentId,
        upvotes,
        downvotes,
        total,
      },
      error: null,
    };
  } catch (error) {
    console.error('Error getting branch vote stats:', error);
    return {
      data: null,
      error: error as PostgrestError,
    };
  }
}

/**
 * 获取用户对分支的投票
 * 
 * @param segmentId 段落ID
 * @returns 用户的投票
 */
export async function getUserBranchVote(
  segmentId: string
): Promise<{ data: BranchVote | null; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  
  if (!user) {
    return {
      data: null,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };
  }

  try {
    const { data, error } = await supabase
      .from('branch_votes')
      .select('id, segment_id, user_id, vote_type, created_at')
      .eq('segment_id', segmentId)
      .eq('user_id', user.id)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 is "No rows returned" error
      throw error;
    }

    return { data: data as BranchVote || null, error: null };
  } catch (error) {
    console.error('Error getting user branch vote:', error);
    return {
      data: null,
      error: error as PostgrestError,
    };
  }
}

/**
 * 投票分支
 * 
 * @param segmentId 段落ID
 * @param voteType 投票类型
 * @returns 操作结果
 */
export async function voteBranch(
  segmentId: string,
  voteType: 'up' | 'down'
): Promise<{ data: BranchVote | null; error: PostgrestError | null }> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  
  if (!user) {
    return {
      data: null,
      error: {
        message: 'User not authenticated',
        details: '',
        hint: '',
        code: '401',
      } as PostgrestError,
    };
  }

  try {
    // 检查用户是否已经投票
    const { data: existingVote } = await getUserBranchVote(segmentId);

    if (existingVote) {
      // 如果用户已经投了相同类型的票，则取消投票
      if (existingVote.vote_type === voteType) {
        const { error } = await supabase
          .from('branch_votes')
          .delete()
          .eq('id', existingVote.id);

        if (error) {
          throw error;
        }

        return { data: null, error: null };
      } else {
        // 如果用户投了不同类型的票，则更新投票
        const { data, error } = await supabase
          .from('branch_votes')
          .update({ vote_type: voteType })
          .eq('id', existingVote.id)
          .select('id, segment_id, user_id, vote_type, created_at')
          .single();

        if (error) {
          throw error;
        }

        return { data: data as BranchVote, error: null };
      }
    } else {
      // 如果用户还没有投票，则创建新投票
      const { data, error } = await supabase
        .from('branch_votes')
        .insert({
          segment_id: segmentId,
          user_id: user.id,
          vote_type: voteType,
        })
        .select('id, segment_id, user_id, vote_type, created_at')
        .single();

      if (error) {
        throw error;
      }

      return { data: data as BranchVote, error: null };
    }
  } catch (error) {
    console.error('Error voting branch:', error);
    return {
      data: null,
      error: error as PostgrestError,
    };
  }
}
